# Klaviyo OAuth API Documentation

This document provides an overview of Klaviyo's OAuth API, focusing on the OAuth flow, profile upsert operations, retrieving lists, and adding profiles to lists. The Klaviyo API enables developers to integrate Klaviyo with their applications securely, leveraging OAuth for authentication to enhance security and usability. For detailed API reference, visit [Klaviyo's Developer Portal](https://developers.klaviyo.com).

## Table of Contents
- [Overview](#overview)
- [Authentication with OAuth](#authentication-with-oauth)
  - [OAuth Flow](#oauth-flow)
- [Profile Upsert](#profile-upsert)
  - [Bulk Import Profiles](#bulk-import-profiles)
- [Retrieving Lists](#retrieving-lists)
- [Adding Profiles to Lists](#adding-profiles-to-lists)
  - [Subscribe Profiles to a List](#subscribe-profiles-to-a-list)
  - [Add Profiles to a List](#add-profiles-to-a-list)
- [Rate Limits](#rate-limits)
- [Error Handling](#error-handling)
- [Additional Resources](#additional-resources)

## Overview
Klaviyo's API allows developers to manage profiles, lists, and other resources programmatically. OAuth is recommended for tech partners integrating with Klaviyo due to its enhanced security, improved rate limits, and ability to define API scopes for restricted access. This documentation covers the OAuth flow, key endpoints for profile management, and list operations using OAuth authentication.

## Authentication with OAuth
OAuth provides secure delegated access to Klaviyo APIs via access tokens. To use OAuth, you must register your application, define scopes, and follow the OAuth flow to obtain an access token. The token is included in the `Authorization` header as `Bearer <access_token>`.

Example OAuth request header:
```http
Authorization: Bearer your_access_token
Accept: application/json
revision: 2023-12-15
```

**Note**: Private API keys are an alternative but lack the security and flexibility of OAuth. Always use OAuth for public integrations.

### OAuth Flow
Klaviyo supports the OAuth 2.0 Authorization Code Flow with Proof Key for Code Exchange (PKCE) for secure authentication, suitable for both public and confidential clients. Below is the step-by-step process based on Klaviyo's [OAuth guide](https://developers.klaviyo.com/en/docs/set_up_oauth).

1. **Register Your Application**:
   - Create an app in Klaviyo’s developer portal to obtain a `client_id` and `client_secret`.
   - Specify a redirect URI where users will be sent after authorization.
   - Define required scopes (e.g., `profiles:read`, `profiles:write`, `lists:read`, `lists:write`) to limit access.

2. **Redirect User to Authorization URL**:
   - Construct an authorization URL to prompt the user to grant access.
   - **Endpoint**: `GET https://www.klaviyo.com/oauth/authorize`
   - **Parameters**:
     - `response_type=code` (required)
     - `client_id` (your app’s client ID)
     - `redirect_uri` (must match the registered URI)
     - `scope` (space-separated list of scopes, e.g., `profiles:write lists:read`)
     - `state` (optional, for CSRF protection)
     - `code_challenge` (PKCE, SHA256 hash of `code_verifier`)
     - `code_challenge_method=S256` (PKCE, required if `code_challenge` is used)

   **Example**:
   ```http
   https://www.klaviyo.com/oauth/authorize?response_type=code&client_id=your_client_id&redirect_uri=https://yourapp.com/callback&scope=profiles:write%20lists:read&state=xyz123&code_challenge=your_code_challenge&code_challenge_method=S256
   ```

   - The user is redirected to Klaviyo’s login page, authenticates, and authorizes your app.
   - On approval, Klaviyo redirects the user to your `redirect_uri` with a `code` and `state` in the query parameters.

3. **Exchange Authorization Code for Access Token**:
   - Use the `code` to request an access token.
   - **Endpoint**: `POST https://a.klaviyo.com/oauth/token`
   - **Headers**:
     ```http
     Content-Type: application/x-www-form-urlencoded
     ```
   - **Body Parameters**:
     - `grant_type=authorization_code`
     - `code` (authorization code from redirect)
     - `client_id`
     - `client_secret` (omit for public clients)
     - `redirect_uri`
     - `code_verifier` (PKCE, the original verifier for `code_challenge`)

   **Example Request**:
   ```bash
   curl -X POST 'https://a.klaviyo.com/oauth/token' \
     -H 'Content-Type: application/x-www-form-urlencoded' \
     -d 'grant_type=authorization_code&code=your_auth_code&client_id=your_client_id&client_secret=your_client_secret&redirect_uri=https://yourapp.com/callback&code_verifier=your_code_verifier'
   ```

   **Response**:
   ```json
   {
     "access_token": "your_access_token",
     "token_type": "Bearer",
     "expires_in": 3600,
     "refresh_token": "your_refresh_token",
     "scope": "profiles:write lists:read"
   }
   ```

4. **Refresh the Access Token**:
   - Access tokens expire (e.g., after 3600 seconds). Use the `refresh_token` to obtain a new access token.
   - **Endpoint**: `POST https://a.klaviyo.com/oauth/token`
   - **Body Parameters**:
     - `grant_type=refresh_token`
     - `refresh_token`
     - `client_id`
     - `client_secret` (omit for public clients)

   **Example Request**:
   ```bash
   curl -X POST 'https://a.klaviyo.com/oauth/token' \
     -H 'Content-Type: application/x-www-form-urlencoded' \
     -d 'grant_type=refresh_token&refresh_token=your_refresh_token&client_id=your_client_id&client_secret=your_client_secret'
   ```

   **Response**:
   ```json
   {
     "access_token": "new_access_token",
     "token_type": "Bearer",
     "expires_in": 3600,
     "refresh_token": "new_refresh_token",
     "scope": "profiles:write lists:read"
   }
   ```

**Notes**:
- Store `access_token` and `refresh_token` securely.
- Use PKCE to prevent code interception attacks, especially for public clients (e.g., mobile apps).
- Validate `state` on redirect to prevent CSRF attacks.
- If scopes change, reauthorize the user to update permissions.

## Profile Upsert
Profiles represent contacts in your Klaviyo account. The API supports creating or updating profiles (upsert) via the Bulk Import Profiles endpoint.

### Bulk Import Profiles
The `/api/profile-bulk-import-jobs/` endpoint performs an upsert operation, creating new profiles or updating existing ones based on identifiers like email or phone number. You can also add profiles to a list during import.

**Endpoint**: `POST https://a.klaviyo.com/api/profile-bulk-import-jobs/`

**Scopes Required**: `profiles:write`, `lists:write` (if adding to a list)

**Request Example**:
```json
{
  "data": {
    "type": "profile-bulk-import-job",
    "attributes": {
      "profiles": {
        "data": [
          {
            "type": "profile",
            "attributes": {
              "email": "<EMAIL>",
              "phone_number": "+***********",
              "first_name": "John",
              "last_name": "Doe",
              "properties": {
                "favorite_color": "blue"
              }
            }
          }
        ]
      }
    },
    "relationships": {
      "lists": {
        "data": [
          {
            "type": "list",
            "id": "your_list_id"
          }
        ]
      }
    }
  }
}
```

**Response**:
- **200 OK**: Job created. Some profiles may be dropped (e.g., duplicates). Check `/api/profile-bulk-import-jobs/{job_id}/profiles/` for successful profiles.
- **400 Bad Request**: Invalid identifiers or payload size exceeds 500 KB.

**Notes**:
- At least one identifier (email, phone number, or external_id) is required.
- Duplicates are dropped and listed in import errors.
- Consent status is not updated via this endpoint. Use the Subscribe Profiles endpoint for consent.

## Retrieving Lists
Lists are used to organize profiles for campaigns or flows. The Lists API allows you to retrieve all lists in your account.

**Endpoint**: `GET https://a.klaviyo.com/api/lists/`

**Scopes Required**: `lists:read`

**Request Example**:
```bash
curl --get 'https://a.klaviyo.com/api/lists/' \
  --header 'Authorization: Bearer your_access_token' \
  --header 'Accept: application/json' \
  --header 'revision: 2023-12-15'
```

**Response Example**:
```json
{
  "data": [
    {
      "type": "list",
      "id": "list_id_1",
      "attributes": {
        "name": "Newsletter Subscribers",
        "created": "2023-01-01T12:00:00+00:00",
        "updated": "2023-12-01T12:00:00+00:00"
      },
      "links": {
        "self": "https://a.klaviyo.com/api/lists/list_id_1/"
      }
    }
  ],
  "links": {
    "self": "https://a.klaviyo.com/api/lists/",
    "next": null,
    "prev": null
  }
}
```

**Notes**:
- Use cursor-based pagination (`?page[cursor]`) for large datasets.
- Filter lists by fields like `name` or `created` if needed.

## Adding Profiles to Lists
Klaviyo provides two endpoints for adding profiles to lists: one for subscribing with consent and another for direct addition.

### Subscribe Profiles to a List
The `/api/v2/list/{list_id}/subscribe` endpoint subscribes profiles to a list, respecting the list’s opt-in settings (single or double opt-in).

**Endpoint**: `POST https://a.klaviyo.com/api/v2/list/{list_id}/subscribe`

**Scopes Required**: `profiles:write`, `lists:write`

**Request Example**:
```json
{
  "data": [
    {
      "type": "profile",
      "attributes": {
        "email": "<EMAIL>",
        "subscriptions": {
          "email": {
            "marketing": {
              "consent": "SUBSCRIBED"
            }
          }
        }
      }
    }
  ]
}
```

**Response**:
- **200 OK**: Profiles subscribed or queued for double opt-in confirmation.
- **Empty Array**: If double opt-in is enabled, profiles are not subscribed until confirmed.

**Notes**:
- Check the list’s opt-in settings (`single` or `double`) in your Klaviyo account.
- Double opt-in requires user confirmation via email, so profiles may not appear subscribed immediately.

### Add Profiles to a List
The `/api/v2/list/{list_id}/members` endpoint adds profiles to a list immediately, equivalent to a CSV upload. Use this for non-consent-based additions.

**Endpoint**: `POST https://a.klaviyo.com/api/v2/list/{list_id}/members`

**Scopes Required**: `profiles:write`, `lists:write`

**Request Example**:
```json
{
  "data": [
    {
      "type": "profile",
      "id": "profile_id_1"
    }
  ]
}
```

**Response**:
- **204 No Content**: Profiles added successfully.
- **400 Bad Request**: Invalid profile IDs or exceeding 1000 profiles per call.

**Notes**:
- Maximum 1000 profiles per request.
- Use Subscribe Profiles for consent-based subscriptions instead.

## Rate Limits
OAuth integrations have improved rate limits compared to private keys:
- **General**: Burst: 75/s, Steady: 700/m
- **With `additional-fields[profile]=predictive_analytics`**: Burst: 10/s, Steady: 150/m
- **With `include=list` or `include=segment`**: Burst: 1/s, Steady: 15/m

Always check the `Rate-Limit` headers in responses to manage throttling.

## Error Handling
Common errors include:
- **400 Bad Request**: Invalid payload, missing identifiers, or age-gating issues (e.g., missing `age_gated_date_of_birth` for SMS).
- **429 Too Many Requests**: Exceeded rate limits. Implement exponential backoff.
- **403 Forbidden**: Insufficient scopes or invalid token.

Check Klaviyo’s [Rate limits, status codes, and errors guide](https://developers.klaviyo.com/en/docs/rate_limits_and_error_handling) for details.

## Additional Resources
- [Klaviyo API Reference](https://developers.klaviyo.com/en/reference/api-overview)
- [Klaviyo OAuth Guide](https://developers.klaviyo.com/en/docs/set_up_oauth)
- [Klaviyo Community](https://community.klaviyo.com)
- [YouTube Tutorials](https://www.youtube.com/playlist?list=your_klaviyo_playlist)

For API support, contact Klaviyo’s support team via the [Help Center](https://help.klaviyo.com).