import { createSSRApp } from 'vue';
import hu from '../../om-admin/src/translations/hu.json';
import en from '../../om-admin/src/translations/en.json';
import App from './components/App.vue';
import Quill from './components/Quill.vue';
import { createI18n } from './i18n.js';

// Cache store based on the locale and the fistPage values.
const _i18n = {
  hu: null,
  en: null,
};

const _getI18N = (locale = 'en') => {
  const SUPPORTED_LOCALES = ['en', 'hu'];
  const FALLBACK_LOCALE = 'en';

  // Use fallback locale if the requested locale is not supported
  const validLocale = SUPPORTED_LOCALES.includes(locale) ? locale : FALLBACK_LOCALE;

  if (_i18n[validLocale]) {
    return _i18n[validLocale];
  }

  const i18n = createI18n({
    locale: validLocale,
    messages: { en, hu },
  });
  _i18n[validLocale] = i18n;

  return i18n;
};

// Generates a key for the memory cache based on the locale and the fistPage

// Returns the cached VueApp from the store
const getVueApp = async ({ firstPage = false, locale = 'en', store }) => {
  const app = createSSRApp(App, { firstPage });
  app.component('QuillEditor', Quill);
  const i18n = _getI18N(locale);
  app.use(store);
  app.use(i18n);
  return app;
};

export { getVueApp };
