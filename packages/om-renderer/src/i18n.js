export const createI18n = ({ locale, messages }) => {
  const FALLBACK_LOCALE = 'en';

  return {
    install: (app) => {
      app.config.globalProperties.$t = (key) => {
        // Try to get translation from the requested locale
        const getTranslation = (fromLocale) => {
          return key.split('.').reduce((o, i) => {
            return o ? o[i] : undefined;
          }, messages[fromLocale]);
        };

        const translation = getTranslation(locale);
        if (translation !== undefined) {
          return translation;
        }

        // If translation doesn't exist and we're not already using fallback locale,
        // try to get translation from fallback locale
        if (locale !== FALLBACK_LOCALE && messages[FALLBACK_LOCALE]) {
          const fallbackTranslation = getTranslation(FALLBACK_LOCALE);
          if (fallbackTranslation !== undefined) {
            return fallbackTranslation;
          }
        }

        // If no translation found, return the key itself
        return key;
      };
    },
  };
};
