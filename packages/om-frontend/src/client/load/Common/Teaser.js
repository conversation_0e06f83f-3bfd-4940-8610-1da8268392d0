import { OptiMonk } from '../../OptiMonk';
import sidebarAsPopupDisplay from '../DisplayHandler/SidebarAsPopupDisplay';
import { $$ } from '../shared/dom';
import { platform } from '../../preload/shared/device';

const inversePositions = {
  bottom: 'top',
  top: 'bottom',
};

const isMobile = window.OptiMonkRegistry.isMobile;
export const Teaser = {
  tabAttentionSeekerIntervals: {},

  init(campaign) {
    if (!campaign.isTabbed()) {
      return;
    }
    campaign.getTabElement()?.setAttribute('aria-label', 'optimonk');

    OptiMonk.addListener(campaign.getTabElement(), 'click', function (e) {
      if (
        e.target.classList.contains('om-teaser-close') ||
        e.target.classList.contains('om-teaser-close-x')
      ) {
        campaign.reportEvent('optimonk#teaser-click-close');
        return;
      }
      campaign.restoreMinimized();
      const campaignCookie = campaign.getCookie();
      if (!campaign.isActivated()) {
        campaignCookie.incrementNumberOfDisplays();
      }
      campaign.setActivated();
      OptiMonk.sendMetrics('teaser-click', { campaignId: campaign.getId() });
      campaign.reportEvent('optimonk#teaser-click-show');
      if (campaign.setShowed) {
        campaign.setShowed();
      }
    });
  },
  showTeaser(campaign) {
    const cookie = campaign.getCookie();

    if (!cookie.getNumberOfDisplays() && cookie.isBeforeTeaserClosed()) {
      return false;
    }
    if (
      cookie.getNumberOfDisplays() &&
      (cookie.isAfterTeaserClosed() || !campaign.isTabbedAfterPopup())
    ) {
      return false;
    }

    setTimeout(function () {
      const rect = campaign.getTabElement().getBoundingClientRect();
      const sizeParams = {
        width: `${rect.width}px`,
        height: `${rect.height}px`,
      };
      campaign.minimize(sizeParams);
      campaign.getCookie().setTeaserShow();
    }, 0);
    campaign.reportEvent('optimonk#teaser-show');
    campaign.getCookie().setAppearance();

    if (OptiMonkRegistry.isMobile && campaign.minimized) {
      campaign.addViewportSizeListener();
    }

    return true;
  },
  close(campaign) {
    this.hideTabElement(campaign);

    const cookie = campaign.getCookie();
    if (!cookie.getNumberOfDisplays()) {
      cookie.setBeforeTeaserClosed();
      return;
    }
    cookie.setPermanentTeaserClosed();
    cookie.setStateClosed();

    if (campaign.getFullscreenClass() && OptiMonkRegistry.isMobile) {
      const element = campaign.getPopupOverlay().querySelector('.om-overlay-center');
      element.style.height = null;
    }
  },
  hide(campaign) {
    OptiMonk.CSS.setStyles(campaign.getOuterHolderElement(), { width: null, height: null });
    const tabElement = campaign.getTabElement();
    const innerHolderElement = campaign.getHolderElement();
    let tabPosition = tabElement.getAttribute('data-tab-position');
    tabPosition = tabPosition ? tabPosition.split('-') : false;
    const holderCss = {
      'pointer-events': null,
      height: null,
    };

    if (tabPosition) {
      const verticalPosition = tabPosition[0];
      holderCss[inversePositions[verticalPosition]] = null;
    }
    OptiMonk.CSS.setStyles(innerHolderElement, holderCss);

    this.animateSwitch(campaign, () => {
      if (!campaign.minimized) {
        this.hideTabElement(campaign);
      }
      this.clearAttentionSeekerInterval(campaign);
    });
  },
  hideTabElement(campaign) {
    const tabElement = campaign.getTabElement();
    const holderElement = campaign.getOuterHolderElement();
    OptiMonk.CSS.style(tabElement, 'display', 'none');
    OptiMonk.CSS.style(tabElement, 'visibility', 'hidden');

    // when permanent teaser is enabled, we shouldn't have to remove pointer-events CSS property
    if (sidebarAsPopupDisplay.isPopup(campaign.getId()) && !campaign.isPermanentTeaser()) {
      OptiMonk.CSS.style(holderElement, 'pointer-events', null);
    }
    if (OptiMonkRegistry.isMobile && campaign.minimized) {
      campaign.removeViewportSizeListener();
    }
  },
  show(campaign, sizeParams) {
    this.showTabElement(campaign, sizeParams);
    this.animateSwitch(campaign, () => {
      this.animateAttentionSeeker(campaign);
    });
  },
  showTabElement(campaign, sizeParams) {
    const overlayElement = campaign.getOverlayElement();
    const tabElement = campaign.getTabElement();
    const holderElement = campaign.getHolderElement();
    let holderCSS;
    const overlayCss = {
      background: 'transparent',
      margin: `${sizeParams.margin}!important`,
      'pointer-events': 'none',
      overflow: 'hidden',
    };
    OptiMonk.CSS.setStyles(tabElement, {
      display: 'flex',
      visibility: 'visible',
      top: null,
      left: null,
      bottom: null,
      right: null,
    });
    const isCentered = /(top|bottom)-center/.test(
      tabElement.getAttribute('data-tab-position') || '',
    );
    const pointerEvents = isCentered ? 'none' : 'all';
    OptiMonk.CSS.style(tabElement, 'pointer-events', pointerEvents);
    let tabPosition = tabElement.getAttribute('data-tab-position');
    tabPosition = tabPosition ? tabPosition.split('-') : false;
    if (tabPosition) {
      const verticalPosition = tabPosition[0];
      const horizontalPosition = tabPosition[1];

      overlayCss[verticalPosition] = '0';
      overlayCss[horizontalPosition] = '0';
      holderCSS = {};
      holderCSS[verticalPosition] = '0';
      holderCSS[horizontalPosition] = '0';
      holderCSS[inversePositions[verticalPosition]] = 'unset';
    }

    const isRectangleTabOrIsV2Teaser =
      tabElement.classList.contains('om-tab-basic') ||
      tabElement.classList.contains('om-tab-wrapper-v2');
    if (isMobile && isRectangleTabOrIsV2Teaser) {
      const rect = tabElement.getBoundingClientRect();
      holderCSS.width = '100% !important';
      holderCSS.height = `${rect.height}px`;
    }
    holderCSS = {
      ...holderCSS,
      'pointer-events': 'none',
      height: '100%',
      bottom: '0',
      width:
        (tabPosition.includes('center') &&
          (tabPosition.includes('top') || tabPosition.includes('bottom'))) ||
        isMobile
          ? '100%'
          : null,
    };
    OptiMonk.CSS.setStyles(holderElement, holderCSS);
    OptiMonk.CSS.setStyles(overlayElement, overlayCss);
  },
  animateSwitch(campaign, callback) {
    const tabElement = campaign.getTabElement();
    let tabSwitchAnimation = tabElement.getAttribute('data-switch-animation');
    if (platform.isIphone) {
      tabSwitchAnimation = 'fadeIn';
    }
    if (tabSwitchAnimation) {
      const tabSwitchAnimationOpposite =
        OptiMonk.Util.AnimateCssHelper.getOppositeAnimation(tabSwitchAnimation);
      tabElement.classList.remove(
        `om-${campaign.minimized ? tabSwitchAnimationOpposite : tabSwitchAnimation}`,
      );
      if (callback) {
        $$.one(tabElement, 'animationend', callback);
      }
      tabElement.classList.add(
        'om-animated',
        `om-${campaign.minimized ? tabSwitchAnimation : tabSwitchAnimationOpposite}`,
      );
    } else if (callback) {
      callback();
    }
  },
  animateAttentionSeeker(campaign) {
    const tabElement = campaign.getTabElement();
    const tabAnimationEl = tabElement.querySelector('.om-tab-animation');
    const attentionSeekerType = tabElement.getAttribute('data-attention-seeker-type');
    let attentionSeekerFreq = tabElement.getAttribute('data-attention-seeker-freq');
    attentionSeekerFreq = parseInt(attentionSeekerFreq, 10);
    if (
      tabAnimationEl &&
      attentionSeekerType &&
      attentionSeekerFreq &&
      this.tabAttentionSeekerIntervals[campaign.getId()] === undefined
    ) {
      this.tabAttentionSeekerIntervals[campaign.getId()] = setInterval(() => {
        tabAnimationEl.classList.add('om-animated', `om-${attentionSeekerType}`);
        $$.one(tabAnimationEl, 'animationend', () => {
          tabAnimationEl.classList.remove('om-animated', `om-${attentionSeekerType}`);
        });
      }, attentionSeekerFreq * 1000);
    }
  },
  clearAttentionSeekerInterval(campaign) {
    const tabAnimationEl = campaign.getTabElement().querySelector('.om-tab-animation');
    if (tabAnimationEl) {
      tabAnimationEl.classList.remove('om-animated');
      clearInterval(this.tabAttentionSeekerIntervals[campaign.getId()]);
      this.tabAttentionSeekerIntervals[campaign.getId()] = undefined;
    }
  },
};
