import { run } from '@zootools/email-spell-checker';
import { OptiMonk } from '../../OptiMonk';
import { updatePopUpLastSeen } from '../../preload/firstPartyData';
import { getCurrentExperiments, getExperimentGroupId } from '../../preload/Util/Experiments';
import { getProp } from '../../preload/Util/utils';
import {
  DIV_PREFIX,
  FRONTEND_NANOBAR,
  FRONTEND_POPUP,
  FRONTEND_SIDEBAR,
  INLINE_CAMPAIGN,
} from '../../shared/Campaign/campaignConstans';
import { CampaignProgressState } from '../../shared/CampaignProgressState';
import { TYPE_SHOP } from '../../shared/DTR/Components/consts';
import { DDTR } from '../../shared/DTR/DDTR';
import { Replacer } from '../../shared/DTR/Replacer';
import { getUsedFontFamilies, isNewFrequencyRuleEnabled } from '../../shared/helpers';
import { CloseGestures } from '../CloseGestureHandler';
import { ButtonEventHandler } from '../Common/ButtonEventHandler';
import { commonCollectValues } from '../Common/CollectCampaignValues';
import { commonGetFirstFilledEmailInput } from '../Common/GetFirstFilledEmailInput';
import { ScratchCardEvents } from '../Common/ScratchCartEvents';
import { Tooltip } from '../Common/Tooltip';
import { Factory as VideoFactory } from '../Common/Video/Factory';
import { WheelEvents } from '../Common/WheelEvents';
import { hideDisplay } from '../DisplayHandler/Display';
import {
  HandlerFactory as DisplayHandlerFactory,
  TYPE_INLINE,
} from '../DisplayHandler/HandlerFactory';
import KeyboardDetection from '../DisplayHandler/KeyboardDetection';
import { $$ } from '../shared/dom';
import { Campaign } from './Campaign';
import { ClosePopup } from './Inline/ClosePopup';
import { CloseTeaser } from './Inline/CloseTeaser';
import { InlineTextReplacer } from './Recart/InlineTextReplacer';
import { InputValidator } from './Validator/InputValidator';
import { ImageWithRedirect } from '../Common/ImageWithRedirect';
import { getCanonicalUrl, getDeviceType } from '../../shared/JF/helpers';
import { reportEventJF } from '../../shared/JFEventReport';
import { SpellCheckHelper } from '../../shared/SpellCheckHelper';
import { CONFIG, DOMAINS } from '../../shared/Common/EmailSuggestion';

const Registry = window.OptiMonkRegistry;

export class InlineCampaign extends Campaign {
  constructor(...args) {
    super(...args);
    this.activeInput = null;
    this.closed = true;
    this.minimized = false;
    this.converted = false;
    this.countdowns = [];
    this.coupons = [];
    this.data = {
      pageUserId: '',
      converted: false,
    };
    this.feedbackData = [];
    this.pickAPresent = null;
    this.products = [];
    this.reportedFeedbacks = [];
    this.scratchCards = [];
    this.showReported = false;
    this.tabSettings = null;
    this.tplXHR = null;
    this.type = INLINE_CAMPAIGN;
    this.videos = [];
    this.wheels = [];
    this.closable = true;
    this.imagesLoaded = false;
    this.fontsLoaded = false;
    this.assetsLoading = 0;
    this.queueingForDisplay = false;
  }

  addViewportSizeListener() {
    if (this.getFullscreenClass()) {
      this._boundSetFullscreenOverlayHeight = this.setFullscreenOverlayHeight.bind(this);

      window.visualViewport.addEventListener('resize', this._boundSetFullscreenOverlayHeight, true);
      window.addEventListener('orientationchange', this._boundSetFullscreenOverlayHeight, true);
    }
  }
  removeViewportSizeListener() {
    if (this.getFullscreenClass()) {
      window.visualViewport.removeEventListener(
        'resize',
        this._boundSetFullscreenOverlayHeight,
        true,
      );
      window.removeEventListener('orientationchange', this._boundSetFullscreenOverlayHeight, true);

      this._boundSetFullscreenOverlayHeight = null;
    }
  }

  async createDisplayHandler() {
    this.DisplayHandler = await DisplayHandlerFactory.create(TYPE_INLINE);
  }

  init() {
    this.preparePlaceholder();
    this.loadHTML(this.initCampaign);
    this.data.pageUserId = this.getPageUserId();
  }

  getHTMLFrom(url, resolve, reject) {
    const self = this;
    this.tplXHR = new OptiMonk.native.XMLHttpRequest();
    this.tplXHR.onreadystatechange = function () {
      if (this.readyState === 4) {
        self.tplXHR = null;
      }

      if (this.readyState === 4 && this.status === 200) {
        resolve.call(self, this.responseText);
      } else if (this.readyState === 4 && this.status !== 200) {
        reject.call(self);
      }
    };

    this.tplXHR.open('GET', url, true);
    this.tplXHR.send();
  }

  loadHTML(callback) {
    this.getHTMLFrom(this.getCreativeUrl(), callback, () => {
      this.getHTMLFrom(this.getFallbackCreativeUrl(), callback, console.log);
    });
  }

  preparePlaceholder() {
    const self = this;
    const placeholderElement = OptiMonk.appendBody(this, DIV_PREFIX);
    placeholderElement.innerHTML = self.getInsertHtml();
  }

  initResizeHandler() {
    OptiMonkRegistry.keyboardDetection = new KeyboardDetection();
    window.visualViewport.addEventListener('resize', OptiMonkRegistry.keyboardDetection.listener);
  }

  initCampaign(html) {
    const self = this;
    this.initResizeHandler();
    OptiMonk.requestIdleCallback(
      async () => {
        self.getIFrameElement().innerHTML = html;
        self
          .getIFrameElement()
          .querySelectorAll('script')
          .forEach((script) => {
            script.parentNode.replaceChild(OptiMonk.Util.nodeScriptClone(script), script);
          });
        self.onAfterAppendBody();
        self.initInternal();
        self.initShareButtons();
        self.findCountdowns();
        await self.findVideos();
        await self.findCoupons();
        self.findLuckyWheels();
        self.findScratchCards();
        self.findPickAPresent();
        self.initCustomHTMLElements();
        self.initDateInputs();
        self.initAnimation();
        await self.initProducts();
        self.initialize();
        self.DisplayHandler.setPopupOverlayHeightViewport(self.getId());
        self.initVisibilityChangeDetector();
        self.attachSpellChecker();
      },
      { timeout: 2000 },
    );
  }

  initVisibilityChangeDetector() {
    this.pagesSyncedToIntegration = [];
    const handler = () => {
      const currentPageId = this.getCurrentStep().id;
      if (!this.pagesSyncedToIntegration.includes(currentPageId)) {
        this.sendSavedSubscriber();
        this.pagesSyncedToIntegration.push(currentPageId);
      }
    };

    document.addEventListener('visibilitychange', handler);
  }

  attachSpellChecker() {
    const emailInput = this.getEmailInput();
    if (!emailInput) return;

    let tooltip = null;
    SpellCheckHelper.appendSpellCheckValidator(emailInput);

    const blur = (event) => {
      const email = event.target.value;
      const suggestion = run({ email, domains: DOMAINS, ...CONFIG });
      if (!suggestion) {
        tooltip?.destroy();
        SpellCheckHelper.resetSpellCheckVisibility(event.target);
        return;
      }

      const errorText = event.target.getAttribute('data-error-text');
      event.target.setAttribute('data-old-error-text', errorText);

      const errorTextWithSuggestion = SpellCheckHelper.getErrorMessageByUserAgent(suggestion.full);
      SpellCheckHelper.setErrorTextAttributes(event.target, errorTextWithSuggestion);
      SpellCheckHelper.setSpellCheckVisibility(event.target, 1);

      tooltip = new Tooltip(event.target, true, 'up', true);
      tooltip.show();

      reportEventJF('emailSpellChecker', { input: email, suggestion });
    };

    const focus = (event) => {
      const errorText = event.target.getAttribute('data-old-error-text');
      if (!errorText) return;

      SpellCheckHelper.setErrorTextAttributes(event.target, errorText);
    };

    emailInput.addEventListener('blur', blur);
    emailInput.addEventListener('focus', focus);
  }

  queueForDisplay(allDone) {
    this.queueingForDisplay = true;
    OptiMonk.Util.doMany(
      (todo) => {
        todo((done) => super.queueForDisplay(done));
        // TODO: Remove AssetsLoadedValidator if all campaign types support queueForDisplay()
        todo((done) => this.loadImages(done));
        todo((done) => this.loadFonts(done));
      },
      () => {
        console.log('Queued campaign for display');
        this.queueingForDisplay = false;
        if (allDone) {
          allDone();
        }
      },
    );
  }

  loadImages(done) {
    if (this.imagesLoaded) {
      if (done) done();
      return;
    }
    let imagesLoaded = 0;
    const assetHelperElement = this.getAssetHelperElements();
    if (assetHelperElement) {
      const imageSrcs = JSON.parse(assetHelperElement.getAttribute('data-images'));
      if (!imageSrcs.length) {
        this.imagesLoaded = true;
        if (done) {
          done();
        }
        return;
      }

      imageSrcs.forEach((src) => {
        const image = new Image();
        const handler = () => {
          imagesLoaded += 1;
          if (imagesLoaded === imageSrcs.length) {
            this.imagesLoaded = true;
            if (done) {
              done();
            }
          }
        };
        image.addEventListener('load', handler);
        image.addEventListener('error', handler);
        image.setAttribute('src', src);
      });
    } else {
      if (done) {
        done();
      }
      this.imagesLoaded = true;
    }
  }

  getSmartProductTags() {
    const smartTags = [];
    const elements = this.getOuterCanvasElement().querySelectorAll('.om-element');
    for (let i = 0; i < elements.length; i += 1) {
      const element = elements[i];
      const smartTagAttribute = element.querySelector('[data-smart-tag]');
      if (smartTagAttribute?.dataset.smartTag.includes('smart_product_tag')) {
        smartTags.push(smartTagAttribute?.dataset.smartTag);
      }
    }
    return smartTags;
  }

  loadFonts(done) {
    if (this.fontsLoaded) {
      if (done) done();
      return;
    }
    const assetHelperElement = this.getAssetHelperElements();
    if (assetHelperElement) {
      let usedFonts = JSON.parse(assetHelperElement.getAttribute('data-fonts'));
      let customFonts = JSON.parse(assetHelperElement.getAttribute('data-custom-fonts'));

      if (!usedFonts.length && !customFonts.length) {
        this.fontsLoaded = true;
        if (done) {
          done();
        }
        return;
      }

      if (!window.OptiMonk.WebFont) {
        window.OptiMonk.initWebfontLoader(OptiMonk);
      }

      const WebFontConfig = {
        loading: () => {
          setTimeout(() => {
            if (done && !this.fontsLoaded) {
              done();
            }
            this.fontsLoaded = true;
          }, 2000);
        },
        active: () => {
          if (done) {
            done();
          }
          this.fontsLoaded = true;
        },
        context: window,
      };

      const hasSmartFontLoader = OptiMonkRegistry.features.SMART_FONT_LOADER;
      let loadedFontFamilies;

      if (usedFonts && usedFonts.length) {
        if (hasSmartFontLoader) {
          loadedFontFamilies = getUsedFontFamilies();
          if (loadedFontFamilies.length) {
            usedFonts = usedFonts.filter((font) => {
              const key = font.split(':').shift().replace('+', ' ');
              return !loadedFontFamilies.includes(key);
            });
          }
        }
        if (usedFonts.length) {
          const index = usedFonts.length;
          usedFonts[index - 1] += '&display=swap';
          WebFontConfig.google = {
            families: usedFonts,
          };
        }
      }

      if (customFonts && customFonts.length) {
        if (hasSmartFontLoader) {
          customFonts = customFonts.filter(
            (customFont) => !loadedFontFamilies.includes(customFont),
          );
        }
        const urls = customFonts.map(
          (f) => `${Registry.contentUrl}/customFonts/${Registry.account}/${f}/${f}.css`,
        );

        WebFontConfig.custom = {
          families: customFonts,
          urls,
        };
      }

      OptiMonk.WebFont.load(WebFontConfig);
    } else {
      if (done) {
        done();
      }
      this.fontsLoaded = true;
    }
  }

  getAssetHelperElements() {
    return document.querySelector(`${this.getCampaignContainerSelector()} .om-asset-helper`);
  }

  isAllAssetsLoaded() {
    return this.assetsLoading === 0;
  }

  initialize() {
    const self = this;
    const elementLoadedCb = function () {
      if (self.checkLoadedVideoStatus()) {
        self.setInitialized();
        OptiMonk.triggerEvent(
          document.querySelector('html'),
          'optimonk#campaign-after_initialized',
          { campaign: self },
        );
      }
    };
    if (this.videos.length) {
      this.initYTApi();
    }
    OptiMonk.addListener(this.getCampaignElement(), 'optimonk#video-loaded', elementLoadedCb);
    OptiMonk.addListener(this.getCampaignElement(), 'optimonk#fb-btn-ready', elementLoadedCb);
    elementLoadedCb();
  }

  getClosingDelay() {
    const closeIcon = this.getCampaignElement().querySelector('.om-popup-close-x');
    let closeIconDelay = 0;
    if (closeIcon) {
      const delayAttr = closeIcon.getAttribute('data-delay');
      closeIconDelay = delayAttr ? OptiMonk.parseInt(delayAttr) : 0;
    }
    return closeIconDelay;
  }

  addCloseGestures() {
    CloseGestures.setup(this);
  }

  setInitialized() {
    this.initialized = true;
    this.callCustomJs(this.getId());
  }

  callCustomJs() {
    const fn = window[`OMCustomJS_${this.getId()}`];
    if (fn) {
      OptiMonk.loadScript('/vendor/jquery.min-1.11.3.js', () => {
        fn(OptiMonk, OptiMonk.$, this);
      });
    }
  }

  getPageUserId() {
    return `${window.OptiMonkRegistry.uuid}-${this.creativeId}`;
  }

  sendMessage(type, parameters) {
    const message = {
      optiMonkMsg: 1,
      type,
      parameters,
    };
    OptiMonk.triggerEvent(this.getCampaignElement(), 'event-msg', message);
  }

  getButtonSelector() {
    return `${this.getCampaignContainerSelector()} .om-button:not(.om-button-fallback)`;
  }

  getRadioFeedbackButtonSelector() {
    return `${this.getCampaignContainerSelector()} .om-feedback.om-feedback-button`;
  }

  getSurveyOptionSelector() {
    return `${this.getCampaignContainerSelector()} .om-survey .om-survey-option input`;
  }

  getCampaignContainer() {
    return document.querySelector(this.getCampaignContainerSelector());
  }

  getCampaignElement() {
    return this.getIFrameElement();
  }

  isTabbed() {
    if (this.getPopupOverlay().getAttribute('data-tab') === null) return false;

    const tabSettings = this.getTabSettings();
    const { isMobile } = OptiMonkRegistry;

    if (isMobile && (tabSettings.teaserDevice === 'desktop' || tabSettings.teaserMobile === '0')) {
      return false;
    }

    if (!isMobile && tabSettings.teaserDevice === 'mobile') return false;

    return true;
  }

  getTabSettings() {
    if (this.tabSettings === null) {
      this.tabSettings = this.collectDataFromAttributesList(this.getTabElement());
    }
    return this.tabSettings;
  }

  isTabbedBeforePopup() {
    return this.tabSettings && this.tabSettings.beforePopup !== '0';
  }

  getAfterPageLoadValue() {
    return parseInt(getProp(this.tabSettings, 'afterPageLoadValue', 6), 10) * 1000;
  }

  getAfterValue() {
    return parseInt(getProp(this.tabSettings, 'afterValue', 0), 10) * 1000;
  }

  isTabbedAfterPopup() {
    return this.tabSettings && this.tabSettings.onClose === '1';
  }

  isPermanentTeaser() {
    return this.tabSettings && this.tabSettings.permanent === '1';
  }

  getTabElement() {
    return this.getCampaignElement().querySelector('.om-tab-wrapper, .om-tab-wrapper-v2');
  }

  static replaceText(vars, container, popupsContainer) {
    InlineTextReplacer.vars = vars;
    InlineTextReplacer.replaceAll(container, popupsContainer);
  }

  getIFrameElement() {
    return document.getElementById(`${DIV_PREFIX}-campaign-${this.getId()}`);
  }

  getOverlayElement() {
    return document.getElementById(`${DIV_PREFIX}-overlay-campaign-${this.getId()}`);
  }

  getIFrameContainerElement() {
    return document.getElementById(`${DIV_PREFIX}-iframe-container-campaign-${this.getId()}`);
  }

  getPoweredByOptiMonkElement() {
    return this.getOverlayElement().querySelector('[data-powered-by]');
  }

  getHolderElement() {
    return document.getElementById(`${DIV_PREFIX}-holder-campaign-${this.getId()}`);
  }

  getBoxContainerSelector() {
    return `${this.getCampaignContainerSelector()} .om-outer-canvas .om-canvas`;
  }

  getCanvasElement() {
    return document.querySelector(this.getBoxContainerSelector());
  }

  getOuterCanvasElement() {
    return this.getIFrameElement().querySelector('.om-outer-canvas');
  }

  getCampaignContainerSelector() {
    return `#${DIV_PREFIX}-campaign-${this.getId()}`;
  }

  getEmailInput() {
    const emailInputs = this.getCampaignElement().querySelectorAll('input[name="visitor[email]"]');
    const firstFilledEmailInput = commonGetFirstFilledEmailInput(emailInputs);

    return (
      firstFilledEmailInput ||
      this.getCampaignElement().querySelector('input[name="visitor[email]"]')
    );
  }

  getPopupOverlay() {
    return this.getCampaignElement().querySelector('.om-overlay');
  }

  async findCountdowns() {
    const self = this;
    const elements = this.getCountdownElements();
    if (elements.length === 0) return;

    const { CountDown } = await import('../../shared/Common/CountDown');
    const { CountDownDOM } = await import('../../shared/Common/CountDownDOM');
    elements.forEach(function (element) {
      self.countdowns.push(new CountDown(new CountDownDOM(element), self));
    });
  }

  getCountdownElements() {
    return document.querySelectorAll(this.getCountdownSelector());
  }

  getCountdownSelector() {
    return `${this.getCampaignContainerSelector()} .om-countdown`;
  }

  async findVideos() {
    const self = this;
    const videos = this.getVideoElements();
    const promises = Array.from(videos).map(async (element) => {
      const settings = JSON.parse(element.getAttribute('data-settings'));
      await VideoFactory.initialize(element, settings, self);
      self.videos.push(element);
    });
    return Promise.all(promises);
  }

  getVideoElements() {
    return document.querySelectorAll(this.getVideoSelector());
  }

  getVideoSelector() {
    return `${this.getCampaignContainerSelector()} .om-video-wrapper`;
  }

  async findLuckyWheels() {
    const self = this;
    const wheels = this.getLuckyWheelElements();
    if (wheels.length > 0) {
      const { Wheel } = await import('../Common/Wheel');
      wheels.forEach(function (element) {
        new Wheel(element, self);
        self.wheels.push(element);
      });
    }
  }

  getLuckyWheelElements() {
    return document.querySelectorAll(this.getLuckyWheelSelector());
  }

  getLuckyWheelSelector() {
    return `${this.getCampaignContainerSelector()} .om-lucky-wheel`;
  }

  checkLoadedVideoStatus() {
    let allReady = true;
    this.videos.forEach(function (video) {
      allReady = allReady && video.OMVideo.ready;
    });
    return allReady;
  }

  getIdentificationData() {
    return {
      campaign: {
        id: this.getId(),
        name: this.getName(),
      },
    };
  }

  getSubscribeCB(el) {
    const self = this;
    return function () {
      self.collectValues();
      self.convert({ final: true });
      self.reportConversion(el);
      self.sendFeedback();
    };
  }

  getCreativeUrl() {
    return `${this.creativeUri}&v2=${this.creativeUpdateTimestamp}`;
  }

  getFallbackCreativeUrl() {
    return this.fallbackCreativeUri;
  }

  getConversionUrl() {
    return `${Registry.baseUrl}/public/${
      Registry.account
    }/creative/${this.getCreativeId()}/conversionExtended`;
  }

  getFeedbackUrl() {
    return `${Registry.baseUrl}/public/${
      Registry.account
    }/creative/${this.getCreativeId()}/feedback`;
  }

  getSendSavedSubscriberUrl() {
    return `${Registry.baseUrl}/public/${
      Registry.account
    }/creative/${this.getCreativeId()}/send-saved-subscriber`;
  }

  getSpamProtectionUrl() {
    return `${Registry.baseUrl}/public/${Registry.account}/validate/email`;
  }

  initInternal() {
    const holder = this.getPopupOverlay();
    holder?.setAttribute('aria-label', 'optimonk');
    const boxContainerSelector = this.getBoxContainerSelector();
    const campaignContainer = this.getCampaignContainer();
    const self = this;
    let oldActiveInput;
    const blur = function (e) {
      oldActiveInput = self.activeInput;
      if (self.activeInput) {
        self.activeInput.blur();
        const _noParams = function () {
          const ca = e.target.closest('[id^="om-campaign-"]');
          if (ca) {
            const id = parseInt(ca.id.replace('om-campaign-', ''), 10);
            if (
              id === parseInt(`${self.getId()}`, 10) &&
              (!self.activeInput || self.activeInput === oldActiveInput) &&
              !self.hasWheelSpinning()
            ) {
              self.setClosable(true);
            }
          }
        };
        const _hasParams = function () {
          if (
            e.parameters.campaignId === self.getId() &&
            (!self.activeInput || self.activeInput === oldActiveInput) &&
            !self.hasWheelSpinning()
          ) {
            self.setClosable(true);
          }
        };
        if (e.type === 'touchend') setTimeout(_noParams, 150);
        else setTimeout(_hasParams, 150);
      }
    };
    const html = document.querySelector('html');
    campaignContainer.querySelectorAll(boxContainerSelector).forEach(function (element, index) {
      if (index === 0) {
        element.classList.add('actual');
      } else {
        OptiMonk.displayNone(element);
      }
      element.setAttribute('data-om-step', (index + 1).toString());
    });
    OptiMonk.addListener(campaignContainer, 'optimonk#content-change', function () {
      self.resizeWindow();
    });
    OptiMonk.addListener(campaignContainer, 'optimonk#campaign-show', function () {
      if (self.products.length > 1) {
        setTimeout(self.resizeProducts.bind(self), 150);
        OptiMonk.addListener(window, 'resize', self.resizeProducts.bind(self));
      }
      self.showStep(1, false);
      updatePopUpLastSeen();
      const closeDelay = self.getClosingDelay();
      if (closeDelay > 0) setTimeout(self.addCloseGestures.bind(self), closeDelay * 1000);
    });
    const mobileSwipeables = this.getMobileSwipeableElements();
    if (mobileSwipeables.length) {
      for (let i = 0; i < mobileSwipeables.length; i += 1) {
        const swipeable = mobileSwipeables[i];
        OptiMonk.addListener(swipeable, 'touchstart', (e) => e.stopPropagation());
      }
    }
    OptiMonk.addListener(html, 'optimonk#campaign-popup-input-focus', function (event) {
      self.activeInput = self
        .getCurrentStep()
        .querySelector(`[name="${event.parameters.elementName}"]`);
      if (event.parameters.campaignId === self.getId()) self.setClosable(false);
    });
    OptiMonk.addListener(html, 'touchend', blur);
    OptiMonk.addListener(html, 'optimonk#campaign-popup-input-blur', blur);
    OptiMonk.addListener(html, 'optimonk#campaign-popup-show', function (e) {
      if (e.parameters.campaignId === self.getId()) {
        self.replaceAttributes();
        const tabText = self.getCampaignElement().querySelector('.om-tab-text');
        if (tabText) tabText.innerHTML = Replacer.replaceText(tabText.innerHTML);
        self.resizeProducts();
      }
    });
    this.initPopupButtonListener();
    this.initPoweredBy();
    this.initTab();
    this.initClose();
    this.initPhoneInput();
    this.positionInputsCenterOnMobile();
    if (this.isTabbed() && this.isPermanentTeaser()) {
      new CloseTeaser(this);
    }
    setTimeout(() => {
      if (!this.queueingForDisplay) {
        this.queueForDisplay();
      }
    }, 3400);
    OptiMonk.addListener(html, 'optimonk#gesture', function (e) {
      const sameCampaign = parseInt(e.parameters.caId, 10) === parseInt(self.getId(), 10);
      const overlayClickOnDesktop =
        self.closeGestures.onOverlayClick &&
        e.parameters.type === 'overlay_click' &&
        !Registry.isMobile;
      const onEsc = self.closeGestures.onEsc && e.parameters.type === 'esc';
      if (sameCampaign && (self.closable || overlayClickOnDesktop || onEsc)) {
        const animatedOverlay = self.getAnimationOverlay();
        // eslint-disable-next-line
        const minimize = self.isTabbed() ? self.getTabSettings().onClose == 1 : false;
        const doIt = function () {
          if (animatedOverlay) {
            animatedOverlay.className = animatedOverlay.className
              .replace(' om-animated', '')
              .replace(/ om-fadeOut(Left|Right|Down|Up)/g, '');
            self.closeEvent();
          }
          if (minimize) {
            const rect = self.getTabElement().getBoundingClientRect();
            self.minimize({
              width: `${rect.width}px`,
              height: `${rect.height}px`,
            });
          } else {
            self.closeEvent('closeX');
          }
          if (animatedOverlay) animatedOverlay.removeEventListener('animationend', doIt);
        };
        if (e.parameters.type === 'swipe') {
          if (animatedOverlay) {
            const dir = e.parameters.direction;
            const d = dir.toUpperCase().charAt(0) + dir.substring(1);
            animatedOverlay.addEventListener('animationend', doIt);
            animatedOverlay.className += ` om-animated om-fadeOut${d}`;
            self.closeEvent();
          } else {
            doIt();
          }
        } else {
          doIt();
        }
      }
    });
    OptiMonk.addListener(this.getCampaignElement(), WheelEvents.EVENT_START, () => {
      self.getCampaignElement().classList.add('om-lucky-wheel-spinning');
    });
    OptiMonk.addListener(this.getCampaignElement(), WheelEvents.EVENT_ON_COMPLETE, () => {
      self.getCampaignElement().classList.remove('om-lucky-wheel-spinning');
    });
    OptiMonk.addListener(this.getCampaignElement(), ScratchCardEvents.EVENT_START, () => {
      self.getCampaignElement().classList.add('om-scratch-in-progress');
    });
    OptiMonk.addListener(this.getCampaignElement(), ScratchCardEvents.EVENT_ON_COMPLETE, () => {
      self.getCampaignElement().classList.remove('om-scratch-in-progress');
    });
    if (OptiMonk.platform.isIpad || OptiMonk.platform.isIphone || OptiMonk.platform.isIpod) {
      this.getOuterCanvasElement().classList.add('om-is-ios');
    }
    if (!(OptiMonk.browser.isSafari || OptiMonk.browser.isFirefox || OptiMonk.browser.isChrome)) {
      this.getOuterCanvasElement().classList.add('om-is-other-browser');
    }

    this.addImageLinkListener();
    this.addCartUpdateListener();
  }

  positionInputsCenterOnMobile() {
    const self = this;
    let targetElementId = null;
    function inputMove(event) {
      // The input move cause slow rendering on engage.kraneshares.com
      if (
        OptiMonkRegistry.account === 128430 &&
        window.location.host === 'engage.kraneshares.com'
      ) {
        return;
      }

      const targetElement = event.target;

      targetElementId = targetElement.id;
      if (targetElementId === targetElement.id) return;

      const actualEl = self.getActualContainer();
      const parameters = $$.getOffset(event.target);
      parameters.elementName = targetElement.getAttribute('name');
      parameters.height = $$.height(targetElement);
      parameters.popupHeight = $$.outerHeight(actualEl, true);
      parameters.offsetTop = targetElement.offsetTop;
      parameters.offsetHeight = targetElement.offsetHeight;
      parameters.targetElement = targetElement;

      self.reportEvent(`optimonk#campaign-popup-input-${event.type}`, parameters);
    }
    const focusListener = (event) => {
      setTimeout(function () {
        inputMove(event);
      }, 10);
      setTimeout(function () {
        inputMove(event);
      }, 500);
    };
    const blurListener = (event) => {
      setTimeout(function () {
        inputMove(event);
      }, 10);
    };
    if (!OptiMonk.platform.isIpad && !OptiMonk.platform.isIphone && !OptiMonk.platform.isIpod) {
      this.getFocusableElements().forEach((element) => {
        OptiMonk.addListener(element, 'focus', focusListener);
        OptiMonk.addListener(element, 'blur', blurListener);
      });
    }
  }

  getMobileSwipeableElements() {
    return document.querySelectorAll('.om-mobile-swipe');
  }

  getActualContainer() {
    return document.querySelector(`${this.getBoxContainerSelector()}.actual`);
  }

  getFocusableElements() {
    return document.querySelectorAll('input:not([type="submit"]), textarea');
  }

  getAnimationOverlay() {
    return document.querySelector(`#om-overlay-campaign-${this.getId()} .om-outer-canvas`);
  }

  getFullscreenClass() {
    return this.getCampaignElement()
      ?.querySelector('.om-overlay')
      ?.classList?.contains('om-fullscreen');
  }

  setFullscreenOverlayHeight() {
    const element = this.getPopupOverlay().querySelector('.om-overlay-center');
    element.style.height = '100dvh';
  }

  getWheelClass() {
    return this.getCampaignElement()?.querySelector('.om-overlay')?.classList?.contains('om-wheel');
  }

  getPoweredBy() {
    return this.getCampaignElement().querySelector('.powered-by');
  }

  hidePoweredBy() {
    const poweredBy = this.getPoweredBy();
    if (poweredBy) {
      OptiMonk.CSS.setStyles(poweredBy, { display: 'none', visibility: 'hidden' });
    }
  }

  showPoweredBy() {
    const poweredBy = this.getPoweredBy();
    if (poweredBy) {
      OptiMonk.CSS.setStyles(poweredBy, { display: 'block', visibility: 'visible' });
    }
  }

  initPoweredBy() {
    this.getCampaignElement()
      .querySelectorAll('.powered-by')
      .forEach(function (item) {
        item.remove();
      });
    if (
      OptiMonk.poweredBy &&
      OptiMonk.poweredBy.visible &&
      this.frontendType !== 'nanobar' &&
      this.frontendType !== 'sidebar' &&
      !this.getFullscreenClass() &&
      !this.getWheelClass()
    ) {
      const poweredBy = document.createElement('div');
      const poweredByHref = document.createElement('a');
      const hrefUrl =
        `${OptiMonk.poweredBy.linkBaseUrl}/?utm_source=link&utm_medium=optimonk_popup` +
        `&utm_campaign=${Registry.account}&domain=${location.hostname}`;
      poweredByHref.appendChild(document.createTextNode('Made with ♥️ by OptiMonk'));
      poweredByHref.className = 'powered-by-link';
      poweredByHref.setAttribute('href', hrefUrl);
      poweredByHref.setAttribute('target', '_blank');
      poweredBy.className = 'powered-by';
      poweredBy.appendChild(poweredByHref);
      this.getCampaignElement().appendChild(poweredBy);
    }
  }

  initYTApi() {
    const self = this;
    let checker;
    if (window.YT || document.getElementById('iframe-yt-om')) {
      checker = setInterval(function () {
        if (window.YT && window.YT.Player) {
          self.initYTVideos(checker);
        }
      }, 70);
      return;
    }
    const tag = document.createElement('script');
    tag.id = 'iframe-yt-om';
    tag.src = 'https://www.youtube.com/iframe_api';
    const firstScriptTag = document.getElementsByTagName('script')[0];
    if (firstScriptTag.parentNode) firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    const oldApiReady = window.onYouTubeIframeAPIReady;
    window.onYouTubeIframeAPIReady = function () {
      if (oldApiReady) {
        oldApiReady();
      }
      self.initYTVideos(null);
    };
  }

  initYTVideos(checker) {
    if (checker) {
      clearInterval(checker);
    }
    this.videos.forEach(function (element) {
      element.OMVideo.initPlayer();
    });
  }

  getDTRContentSelector() {
    return `${this.getBoxContainerSelector()} .om-dtr-content`;
  }

  hasShopDTR() {
    const elements = this.getOuterCanvasElement().querySelectorAll('.om-element');
    for (let i = 0; i < elements.length; i += 1) {
      const element = elements[i];
      if (Replacer.hasContentToReplace(element.innerHTML, TYPE_SHOP)) {
        return true;
      }
    }
    return false;
  }

  initPopupButtonListener() {
    const self = this;
    const normalButtons = this.getButtons();
    const radioFeedbackButtons = this.getRadioFeedbacks();
    const surveys = this.getSurveys();
    const buttons = Array.from(normalButtons)
      .concat(Array.from(radioFeedbackButtons))
      .concat(Array.from(surveys));
    buttons.forEach(function (button) {
      new ButtonEventHandler(button, self, false);
    });
  }

  getSurveys() {
    return document.querySelectorAll(this.getSurveyOptionSelector());
  }

  getRadioFeedbacks() {
    return document.querySelectorAll(this.getRadioFeedbackButtonSelector());
  }

  getButtons() {
    return document.querySelectorAll(this.getButtonSelector());
  }

  initShareButtons() {
    const self = this;
    const buttons = this.getCampaignElement().querySelectorAll('.om-social-icon');
    if (buttons.length) {
      this.data.converted = false;
    }
    buttons.forEach(function (button) {
      const link = button.querySelector('a');
      const type = button.getAttribute('data-type');
      const method = button.getAttribute('data-method');
      OptiMonk.addListener(link, 'click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if (method === 'share') {
          OptiMonk.Share[type](link.href);
        } else if (method === 'follow') {
          OptiMonk.Follow.openWindow(link.href);
        }
        self.reportSocialInteraction(method);
      });
    });
  }

  async findScratchCards() {
    const els = this.getCampaignElement().querySelectorAll('.om-scratch-card');
    if (els.length === 0) return;
    const { ScratchCard } = await import('../Common/ScratchCard');
    for (let i = 0; i < els.length; i += 1) {
      const el = els[i];
      const scr = new ScratchCard(el, this);
      this.scratchCards.push(scr);
    }
  }

  async findPickAPresent() {
    const pickAPresentEl = this.getCampaignElement().querySelector('.om-pick-a-present');
    if (pickAPresentEl) {
      const { PickAPresent } = await import('../Common/PickAPresent');
      this.pickAPresent = new PickAPresent(pickAPresentEl, this);
    }
  }

  // eslint-disable-next-line
  resizeCampaignIFrame(width, height) {}

  initClose() {
    new ClosePopup(this);
  }

  loadAsset(assetPaths, callback) {
    const assets = Array.isArray(assetPaths) ? assetPaths : [assetPaths];
    let pending = assets.length;
    const onDone = (path) => (err) => {
      if (err) {
        throw new Error(`Failed to load "${path}" asset script`);
      }
      if (--pending === 0) {
        let isDone = false;
        // Starting 10 sec timeout
        const timeout = setTimeout(() => {
          if (!isDone) {
            console.warn(
              `OptiMonk AssetLoader: initiator did not call done() within 10 seconds when loading assets ${JSON.stringify(
                assets,
              )}.`,
            );
          }
        }, 10000);
        const onDone = () => {
          clearTimeout(timeout);
          this.assetsLoading -= assets.length;
          isDone = true;
        };
        callback(onDone);
      }
    };
    assets.forEach((path) => {
      if (path.endsWith('.js')) {
        OptiMonk.loadScript(path, onDone(path));
      } else if (path.endsWith('.css')) {
        OptiMonk.loadCss(path, onDone(path));
      } else {
        throw new Error(`Unable to determine asset type of "${path}"`);
      }
      this.assetsLoading += 1;
    });
  }

  async initPhoneInput() {
    const self = this;
    const phoneInputs = this.getCampaignElement().querySelectorAll('.intl-tel-input input');
    if (phoneInputs.length > 0) {
      this.loadAsset('/vendors/intlTelInput.min.js', async (done) => {
        const locale = navigator.language || navigator.userLanguage;
        let intlTelInputI18N;
        if (phoneInputs.length && OptiMonk.intlTelInput) {
          if (locale.indexOf('hu') !== -1) {
            const { countryTranslations, interfaceTranslations } = await import(
              `https://cdn.jsdelivr.net/npm/intl-tel-input@${OptiMonk.intlTelInput.version}/build/js/i18n/hu/index.js`
            );
            intlTelInputI18N = { ...countryTranslations, ...interfaceTranslations };
          }
        }
        phoneInputs.forEach(function (phoneInput) {
          let defaultCountry = phoneInput.getAttribute('data-default-country');
          phoneInput.addEventListener('open:countrydropdown', (event) => {
            const wrapper = event?.target?.closest('.om-dropdown');
            wrapper?.classList?.add('active');
            const container = document.querySelector('.iti--container');
            container?.classList?.add('om-iti-container');
          });
          phoneInput.addEventListener('close:countrydropdown', (event) => {
            const wrapper = event?.target?.closest('.om-dropdown');
            wrapper?.classList?.remove('active');
          });
          phoneInput.removeAttribute('disabled');
          if (defaultCountry === 'auto') {
            defaultCountry = self.visitorAdapter.attr('_country_code') || 'us';
          }
          OptiMonk.intlTelInput(phoneInput, {
            i18n: intlTelInputI18N,
            initialCountry: defaultCountry,
          });
          phoneInput.parentElement.classList.add('om-iti-container');
        });
        done();
      });

      Array.from(phoneInputs).map((phoneInput) => phoneInput.setAttribute('pattern', '[0-9]*'));
    }
  }

  close() {
    this.closed = true;

    if (this.tplXHR) this.tplXHR.abort();

    if (this.products.length) {
      OptiMonk.removeListener(window, 'resize', this.resizeProducts);
    }

    this.stopVideos();

    OptiMonk.ActivatedCampaignManager.inactivateCampaign(this.getId());

    return this.DisplayHandler.closeCampaignPopup(this);
  }

  rawClose() {
    const overlayElement = this.getOverlayElement();
    if (OptiMonk.isHidden(overlayElement)) {
      return false;
    }
    hideDisplay(this);
    return true;
  }

  async initTab() {
    const { Teaser } = await import('../Common/Teaser');
    this.getTabSettings();
    Teaser.init(this);
  }

  setShowReported() {
    this.showReported = true;
  }

  isShowReported() {
    return this.showReported;
  }

  async showTeaser() {
    const popupOverlay = this.getPopupOverlay();
    const { Teaser } = await import('../Common/Teaser');
    const willTeaserDisplayed = Teaser.showTeaser(this); // this will call campaign.minimize with tab element size parameters
    if (!willTeaserDisplayed) return;

    this.DisplayHandler.displayCampaign(this.getId());
    this.pauseVideos();
    OptiMonk.CSS.setStyles(popupOverlay, {
      display: 'none',
      visibility: 'visible',
    });
  }

  displayOnEvent(event) {
    const parameters = {
      campaignId: this.getId(),
      event,
    };
    this.displayTrigger(parameters);

    // when frequency enabled let a campaign get back to showed after filled state
    const frequencyCondition = isNewFrequencyRuleEnabled()
      ? this.cookie.getState() === CampaignProgressState.STATE_FILLED
      : false;
    if (this.cookie.getState() === CampaignProgressState.STATE_INIT || frequencyCondition) {
      this.cookie.setShowed();
    }
    this.setActivated();
    if (this.minimized) {
      this.restoreMinimized();
    }
    this.DisplayHandler.displayPopup(this.getId());
    this.showStep(1);
  }

  getTeaserCookieStatus() {
    return this.cookie.getTeaserStatus();
  }

  isTeaserShowing() {
    return this.getTabElement().style.display !== 'none';
  }

  minimize(sizeParams) {
    this.minimized = true;
    this.hidePoweredBy();
    this.pauseVideos();
    this.DisplayHandler.minimize(this, sizeParams);
    this.setClosable(false);
    this.cookie.setTeaserShow();
    this.reportEvent('optimonk#minimize');
  }

  replaceAttributes() {
    const elementsOnPage = this.getCurrentStep().querySelectorAll('.om-element');
    for (let i = 0; i < elementsOnPage.length; i += 1) {
      const element = elementsOnPage[i];
      Replacer.replace(element);
      DDTR.init(this, element);
    }
  }

  restoreMinimized() {
    this.minimized = false;
    this.showPoweredBy();
    this.videos.forEach(function (video) {
      video.OMVideo.playOnRestore();
    });
    this.setClosable(true);
    this.cookie.setTeaserClosed();
    this.replaceAttributes();
    setTimeout(() => {
      this.DisplayHandler.restoreMinimized(this);
      if (!this.isShowReported()) {
        this.displayTrigger({
          campaignId: this.getId(),
          event: 'teaser-click-show',
        });
        OptiMonk.reportActivity(this.getId(), 'showed');
      }
    }, 50);
  }

  asyncValidateOne(input) {
    return InputValidator.validate(this, input);
  }

  getTooltipPosition() {
    return this.isNanobar() && this.getPopupOverlay().classList.contains('top') ? 'down' : 'up';
  }

  validateInputs() {
    const self = this;
    const normalInputs = this.collectAllPageInputs();
    const promises = [];
    let valid = true;
    normalInputs.forEach(function (input) {
      if (OptiMonk.Util.isElementVisible(input)) {
        promises.push(self.asyncValidateOne(input));
      }
    });

    return new Promise((resolve) => {
      Promise.all(promises).then((results) => {
        results.forEach((result) => {
          valid = valid && result;
        });
        const pickerGroups = this.getCurrentStep().querySelectorAll('.om-picker-group');
        pickerGroups.forEach(function (pickerGroup) {
          const pickerGrp = pickerGroup;
          const isRequired = pickerGrp.getAttribute('data-validations').indexOf('required') !== -1;
          const hasTooltip = pickerGrp.OMTooltip;
          if (hasTooltip) {
            pickerGrp.OMTooltip.destroy();
          }
          if (isRequired) {
            const inputs = pickerGrp.querySelectorAll(
              'input[type="radio"], input[type="checkbox"]',
            );
            let v = false;
            inputs.forEach(function (input) {
              const inputElement = input;
              if (inputElement.checked && OptiMonk.Util.isElementVisible(input)) {
                v = true;
              }
            });
            if (!v && hasTooltip) {
              pickerGrp.OMTooltip.init();
            } else if (!v && !hasTooltip) {
              new Tooltip(pickerGrp, true, self.getTooltipPosition());
            }
            valid = v && valid;
          }
        });
        resolve(valid);
      });
    });
  }

  popupValidation() {
    return new Promise((resolve) => {
      this.validateInputs().then((valid) => {
        resolve(valid);
      });
    });
  }

  // Recart fallback form validation
  validateFallback() {
    const self = this;
    const inputs = this.getCampaignElement().querySelectorAll('input.om-fallback-input');
    let result = true;
    const promises = [];
    inputs.forEach(function (input) {
      promises.push(self.asyncValidateOne(input));
    });
    return new Promise((resolve) => {
      Promise.all(promises).then((validationResults) => {
        validationResults.forEach((validationResult) => {
          result = result && validationResult;
        });
        resolve(result);
      });
    });
  }

  hasEmailInputInCurrentStep() {
    return this.getCurrentStep().querySelectorAll('input[name="visitor[email]"]').length > 0;
  }

  hasEmailInputInStep(page) {
    let step = this.getCurrentStep();
    if (page) {
      step = typeof page === 'number' ? this.getStepContainer(page) : page;
    }
    const fallbackContainer = step.querySelector('.om-fallback-container');
    if (fallbackContainer) {
      const hasEmail = step.querySelectorAll('input[name="visitor[email]"]').length > 1;
      const hasPhone = step.querySelectorAll('input.om-phone-input').length >= 1;
      return hasEmail || hasPhone;
    }
    return false;
  }

  getCurrentStep() {
    if (!this.currentStep) {
      this.currentStep = document.querySelector(`${this.getBoxContainerSelector()}.actual`);
    }
    return this.currentStep;
  }

  getCurrentPage() {
    return OptiMonk.parseInt(this.getCurrentStep().getAttribute('data-om-step'));
  }

  getNextPage() {
    return this.getCurrentPage().valueOf() + 1;
  }

  redirect(url) {
    this.closed = true;
    function removeHash(url) {
      return url.indexOf('#') === -1 ? url : url.substring(0, url.indexOf('#'));
    }
    function isOnPageAnchorRedirect(url) {
      return url.indexOf('#') > -1 && removeHash(window.location.href) === removeHash(url);
    }

    url = Replacer.replaceText(url, true); // fix same site path
    if (isOnPageAnchorRedirect(url)) {
      this.closeEvent();
    }
    this.report('redirect', { url });
  }

  dial(phoneNumber) {
    if (phoneNumber) {
      this.close();
      window.open(`tel:${phoneNumber}`, '_system');
    }
  }

  /**
   * @param pageNum starts from 1
   * @param trigger
   */
  showStep(pageNum, trigger = true) {
    this.closed = false;
    this.setClosable(true);
    const stepEl = this.getStepContainer(pageNum);
    if (!stepEl) {
      this.close();
      OptiMonk.triggerEvent(this.getCampaignElement(), 'optimonk#campaign-close', {
        campaignId: this.getId(),
        campaign: this,
        needToReport: false,
      });
      return;
    }
    this.currentStep = undefined;
    this.hidePages();
    stepEl.classList.remove('om-fadeOut');
    stepEl.classList.add('om-animated', 'om-fadeIn');
    stepEl.classList.add('actual');
    stepEl.classList.remove('om-canvas-hidden');
    stepEl.style.removeProperty('display');
    if (trigger) {
      const eventName = 'optimonk#campaign-popup-show';
      this.reportEvent(eventName, { type: `Step: ${pageNum}`, pageNum });
    }
  }

  showNextStep() {
    this.showStep(this.getNextPage());
  }

  hidePages() {
    const pages = this.getCampaignPages();
    pages.forEach(function (el) {
      const element = el;
      element.classList.remove('om-fadeIn');
      element.classList.add('om-animated', 'om-fadeOut');
      element.classList.remove('actual');
      element.style.display = 'none';
    });
  }

  getStepContainer(stepnumber) {
    return document.querySelector(
      `${this.getBoxContainerSelector()}[data-om-step="${stepnumber}"]`,
    );
  }

  resizeWindow() {}

  resize(width, height) {
    const actual = this.getCurrentStep();
    width = width || actual.offsetWidth;
    height = height || actual.offsetHeight;
    this.report('resize', { height, width });
  }

  closeEvent(message) {
    this.closed = true;
    this.stopVideos();
    const type = message || (this.converted ? 'completed' : 'no');
    const handler = OptiMonk.MessageHandler.get('close_campaign_popup');
    OptiMonk.triggerEvent(document.querySelector('html'), 'close_campaign_popup', {
      message: 'close',
      type,
      campaign: this,
    });
    handler.handle({ parameters: { type, campaignId: this.getId() } });
    this.sendSavedSubscriber();
  }

  sendSavedSubscriber() {
    // currently showing
    if (!this.isActivated()) {
      return;
    }
    navigator.sendBeacon(
      this.getSendSavedSubscriberUrl(),
      new URLSearchParams(
        OptiMonk.Util.serializeObject({
          account: Registry.account,
          variantId: this.getCreativeId(),
          pageUserId: this.getPageUserId(),
        }),
      ),
    );
  }

  stopVideos() {
    this.videos.forEach(function (video) {
      video.OMVideo.stop();
    });
  }

  pauseVideos() {
    this.videos.forEach(function (video) {
      video.OMVideo.pause();
    });
  }

  reportEvent(event, parameters) {
    parameters = parameters || {};
    parameters.event = event;
    this.report('event', parameters);
  }

  collectDataFromAttributesList(el) {
    const self = this;
    const attrs = {};
    if (el.hasAttributes()) {
      for (let index = 0, attrLength = el.attributes.length; index < attrLength; index += 1) {
        const attr = el.attributes[index];
        if (attr && attr.name.indexOf('data-') === 0) {
          const name = self.camelCase(attr.name.slice(5));
          attrs[name] = attr.value;
        }
      }
    }
    return attrs;
  }

  camelCase(str) {
    const spStr = str.split('-');
    spStr.forEach(function (s, i) {
      if (!i) return;
      spStr[i] = InlineCampaign.ucFirst(spStr[i]);
    });
    return spStr.join('');
  }

  static ucFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  collectDataFor(el) {
    return el ? (el.dataset ? el.dataset : this.collectDataFromAttributesList(el)) : {};
  }

  reportConversion(element) {
    const elementDetails = {
      id: element.id,
      class: element.class,
      data: this.collectDataFor(element),
      snapshotElementData: this.collectDataFor(element.closest('.OM-conversion-snapshotList')),
    };
    this.report('conversion', {
      // "recommend": self.parameters.recommend, TODO[gygergo]
      elementDetails,
    });
  }

  markFilled() {
    this.report('filled');
  }

  setFilled() {
    const campaignId = this.getId();
    const caEl = document.querySelector('html');
    OptiMonk.triggerEvent(caEl, 'optimonk#campaign-before_mark_filled', { campaignId });
    this.cookie.setFilled();
    OptiMonk.triggerEvent(caEl, 'optimonk#campaign-after_mark_filled', { campaignId });
    this.setLastFilledCookie();
  }

  setLastFilledCookie() {
    const FILLED_COOKIE_EXPIRE_DAYS = 14;
    const date = new Date();
    date.setTime(date.getTime() + FILLED_COOKIE_EXPIRE_DAYS * 24 * 60 * 60 * 1000);
    const expires = `; expires=${date.toUTCString()}`;
    const value = JSON.stringify({
      ts: new Date().getTime(),
      creativeId: this.getCreativeId(),
    });
    document.cookie = `omLastFilled=${value}${expires}; path=/`;
  }

  reportSocialInteraction(method) {
    const data = this.getBaseConversionData();
    data.converted = this.converted;
    data.final = true;
    data.needSetConverted = true;

    // callback determines if campaign is set to filled | empth callback is not filled
    const frequencyEnabled = isNewFrequencyRuleEnabled();
    const callback = frequencyEnabled
      ? () => {
          this.markFilled();
          this.setFilled();
        }
      : () => {};

    this.convert(data, callback);

    this.report('analyticsReport', { type: `social_${method}` });
  }

  validate() {
    return new Promise((resolve) => {
      this.popupValidation().then((isValid) => {
        resolve(isValid);
      });
    });
  }

  convert(data = {}, callback, onSent) {
    const self = this;
    let needSetConverted = true;
    if (typeof data.needSetConverted !== 'undefined' && !data.needSetConverted)
      needSetConverted = false;
    delete data.needSetConverted;
    this.data = OptiMonk.Util.assign(this.data, data);
    this.data.pageUserId = this.getPageUserId(); // get the pageUserId again because of SPA
    return new Promise((resolve) => {
      this.sendData(
        function (value) {
          if (callback) {
            callback(value);
          } else {
            self.markFilled();
          }
          resolve(value);
        },
        needSetConverted,
        onSent,
      );
      if (needSetConverted) {
        this.converted = true;
      }
    });
  }

  submit(cb) {
    this.collectValues();
    this.sendData(cb);
  }

  sendData(cb, needSetConverted, onSent) {
    const xhr = new OptiMonk.native.XMLHttpRequest();
    xhr.open('POST', this.getConversionUrl(), true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 2 && onSent) {
        // HEADERS_RECEIVED
        onSent();
      } else if (xhr.readyState === 4) {
        let returnValue = {};
        try {
          returnValue = JSON.parse(xhr.responseText);
        } catch (e) {
          console.error('parsing of return value failed');
        }
        if (cb) {
          cb(returnValue);
        }
      }
    };
    if (needSetConverted) this.data.converted = this.converted;
    xhr.send(OptiMonk.Util.serializeObject(this.data));
  }

  collectValues() {
    const { collectedData, feedbackData, reportedFeedbacks } = commonCollectValues(
      this.getCampaignElement(),
      this.reportedFeedbacks,
      this.data,
      OptiMonk,
      this.getBaseConversionData(),
    );
    this.feedbackData = feedbackData;
    this.reportedFeedbacks = reportedFeedbacks;
    this.storeFormData();
    return collectedData;
  }

  sendFeedback() {
    if (!this.feedbackData.length) return;
    const xhr = new OptiMonk.native.XMLHttpRequest();
    xhr.open('POST', this.getFeedbackUrl(), true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.send(`feedback=${encodeURIComponent(JSON.stringify(this.feedbackData))}`);
  }

  getBaseConversionData() {
    OptiMonk.SiteInfo.initialize();
    const siteInfo = JSON.stringify(OptiMonk.SiteInfo.collect());
    const experimentGroupId = getExperimentGroupId(this.currentExperimentId);
    const visitorInExperiment = getCurrentExperiments();
    const deviceType = getDeviceType();
    return {
      final: false,
      needSetConverted: false,
      converted: false,
      'visitor[url]': encodeURIComponent(window.location.href),
      'visitor[canonicalUrl]': encodeURIComponent(getCanonicalUrl()),
      deviceType,
      creative: this.creativeId,
      clientId: Registry.clientId,
      siteInfo,
      experimentGroupId,
      visitorInExperiment,
      userAgent: navigator.userAgent,
    };
  }

  report(type, parameters) {
    parameters = parameters || {};
    parameters.campaignId = this.getId();
    parameters.campaign = OptiMonk.campaigns[this.getId()];
    const message = {
      type,
      parameters,
    };
    const handler = OptiMonk.MessageHandler.get(type);
    handler.handle(message);
  }

  collectPageInputs() {
    return this.getCurrentStep().querySelectorAll(
      'input:not(.om-fallback-input), textarea, select',
    );
  }

  collectAllPageInputs() {
    return this.getCurrentStep().querySelectorAll(
      'input:not([type="radio"]):not(.om-fallback-input), textarea, select',
    );
  }

  collectPreviousPageInputs() {
    const allPages = this.getCampaignPages();
    const currentStep = this.getCurrentPage();
    let inputs = [];
    // eslint-disable-next-line
    allPages.forEach((page, idx) => {
      if (parseInt(page.getAttribute('data-om-step'), 10) <= currentStep) {
        const pageInputs = page.querySelectorAll(
          'input:not([type="radio"]):not(.om-fallback-input)',
        );
        inputs = inputs.concat([].slice.call(pageInputs));
      }
    });
    return inputs;
  }

  getCampaignPages() {
    return document.querySelectorAll(this.getBoxContainerSelector());
  }

  isSidebar() {
    return this.frontendType === FRONTEND_SIDEBAR;
  }

  isNanobar() {
    return this.frontendType === FRONTEND_NANOBAR;
  }

  isPopup() {
    return this.frontendType === FRONTEND_POPUP;
  }

  async findCoupons() {
    const coupons = this.getCampaignElement().querySelectorAll('.om-coupon');
    if (coupons.length === 0) return;
    const { Coupon } = await import('../Common/Coupon');
    let hasFollowup = false;

    for (let i = 0; i < coupons.length; i += 1) {
      const coupon = new Coupon(coupons[i], this);
      this.coupons.push(coupon);
      hasFollowup = hasFollowup || coupon.isFollowup();
    }

    if (hasFollowup) {
      this.events[OptiMonk.Event.TYPES.followupCouponInvoke] = {};
    }
  }

  hasCoupon() {
    return !!this.coupons.length;
  }

  hasShopifyAutoCoupon() {
    for (let i = 0, couponLength = this.coupons.length; i < couponLength; i += 1) {
      const actual = this.coupons[i];

      if (actual.isShopifyAuto()) {
        return true;
      }
    }

    return false;
  }

  getGamificationCoupons() {
    return this.coupons.filter((coupon) => coupon.isGamification());
  }

  getCouponsInCurrentPage() {
    const currentPage = this.getCurrentPage();
    return this.getCouponsInPage(currentPage);
  }

  getCouponsInPage(pageNumber) {
    return this.coupons.filter((c) => {
      return c.getPageNumber() === pageNumber;
    });
  }

  getCoupons() {
    return this.coupons;
  }

  getFollowupCoupons() {
    return this.coupons.filter((coupon) => coupon.isFollowup());
  }

  pageHasScratchCard() {
    let result = false;
    const cp = this.getCurrentPage();
    this.scratchCards.forEach((card) => {
      if (card.getPageNumber() === cp) result = !card.isCompleted();
    });
    return result;
  }

  pageHasPickAPresent() {
    let ret = false;
    if (this.pickAPresent && this.pickAPresent.getPageNumber() === this.getCurrentPage()) {
      ret = !this.pickAPresent.isCompleted();
    }
    return ret;
  }

  nextPageHasScratchCard() {
    let result = false;
    const cp = this.getCurrentPage();
    this.scratchCards.forEach((card) => {
      if (card.getPageNumber() === cp + 1) result = !card.isCompleted();
    });
    return result;
  }

  isScratchStarted() {
    let result = false;
    this.scratchCards.forEach((card) => {
      if (card.isStarted() && !card.isCompleted()) {
        result = true;
      }
    });
    return result;
  }

  async lockCoupons() {
    let result = true;
    // eslint-disable-next-line no-restricted-syntax
    for (const coupon of this.coupons) {
      const response = await coupon.lockCoupon();
      result = result && response;
    }

    return result;
  }

  async initAnimation() {
    const overlay = this.getPopupOverlay();
    const anim = JSON.parse(overlay.getAttribute('data-animation'));
    new OptiMonk.Animations.OverlayAnimation(this);
    if (anim && anim.type === 'snowing') {
      const { Snowing } = await import('../Animations/Snowing');
      new Snowing(this, anim.count, anim.isInterstitial, anim.placement);
    }
  }

  async initProducts({ reInit } = {}) {
    const rawProducts = this.getCampaignElement().querySelectorAll('.om-product');
    if (rawProducts.length === 0) return;
    const { Product } = await import('../Common/Product');
    const collectedProducts = Product.collectElements(this);
    if (reInit) {
      const reInitializedCartRecoProducts = collectedProducts.filter(
        (product) => product.getMode() === 'products-in-the-cart',
      );
      const oldProductComponentsToBeKept = this.products.filter(
        (product) => product.getMode() !== 'products-in-the-cart',
      );
      this.products = [...oldProductComponentsToBeKept, ...reInitializedCartRecoProducts];
    } else {
      this.products = collectedProducts;
    }
    Product._removeProductsSize();
    Product.initProducts(this, { reInit }).then(
      () => console.log('[OM:DEBUG] initialized product components'),
      (err) => console.log(err),
    );
  }

  replaceText(str, inputUri = false, outputUri = false) {
    return Replacer.replaceText(str, inputUri, outputUri);
  }

  setClosable(value) {
    this.closable = value;
  }

  isClosable() {
    return this.closable;
  }

  isProductsConnectedToShop() {
    let isConnected = false;
    if (this.products.length) {
      this.products.forEach((product) => {
        if (product.isConnectedToShop()) isConnected = true;
      });
    }
    return isConnected;
  }

  getProducts() {
    return this.products;
  }

  async resizeProducts() {
    if (this.products.length > 1) {
      const { Product } = await import('../Common/Product');
      Product.resize(this.isTabbed());
    }
  }

  initCustomHTMLElements() {
    const iframes = this.getCustomHTMLElements();
    iframes.forEach((iframe) => {
      const html = decodeURIComponent(escape(atob(iframe.getAttribute('data-custom-html'))));
      iframe.removeAttribute('data-custom-html');
      const iframedoc =
        iframe.contentDocument || (iframe.contentWindow && iframe.contentWindow.document);
      if (iframedoc) {
        iframedoc.open();
        iframedoc.write(
          `<style>html, body {margin: 0; height: 100%; overflow: hidden;}</style>${html}`,
        );
        iframedoc.close();
      }
    });
  }

  getCustomHTMLElements() {
    return document.querySelectorAll('[data-custom-html]');
  }

  initDateInputs() {
    const dateInputs = this.getDateInputs();
    if (dateInputs.length > 0) {
      this.loadAsset(['/vendors/flatpickr.min.js', '/vendors/flatpickr.min.css'], (done) => {
        const formatByLocale = {
          hu: {
            dMY: 'Y. M. d.',
            dFY: 'Y. F d.',
            Ymd: 'Y-m-d',
            DJMY: 'Y. M. j. D.',
          },
          en: {
            dMY: 'd M Y',
            dFY: 'd F Y',
            Ymd: 'Y-m-d',
            JMY: 'J M Y',
            DJMY: 'D J M Y',
          },
        };
        dateInputs.forEach((input) => {
          const format = input.getAttribute('data-format');
          const locale = input.getAttribute('data-locale') || 'en';
          OptiMonk.flatpickr(input, {
            dateFormat: formatByLocale[locale][format],
            locale,
            disableMobile: 'true',
          });
        });
        done();
      });
    }
  }

  getDateInputs() {
    return document.querySelectorAll('.om-date-input');
  }

  needViewportModification() {
    return (
      this.isPopup() || (this.isSidebar() && this.DisplayHandler.isSidebarAsPopup(this.getId()))
    );
  }

  hasWheelSpinning() {
    let result = false;
    this.wheels.forEach((wheel) => {
      if (wheel.OMWheel.isSpinning()) {
        result = true;
      }
    });
    return result;
  }

  isConverted() {
    return this.converted;
  }

  getNextPageContainer() {
    const nextPage = this.getNextPage();
    return this.getStepContainer(nextPage);
  }

  noMoreInputOn(pageNumber) {
    const container = this.getStepContainer(pageNumber);
    if (!container) return true;
    const hasInputs = container.querySelectorAll('input').length > 0;
    const hasTextareas = container.querySelectorAll('textarea').length > 0;
    const hasDropdown = container.querySelectorAll('select').length > 0;
    return !hasInputs && !hasTextareas && !hasDropdown;
  }

  noMoreNextPageButtonOn(pageNumber) {
    const container = this.getStepContainer(pageNumber);
    if (!container) return true;
    const events = [
      ButtonEventHandler.ACTIONS_NEXT_POPUP,
      ButtonEventHandler.ACTIONS_JUMP_TO_PAGE,
      ButtonEventHandler.ACTIONS_BASED_ON_FEEDBACK,
    ];
    const buttons = container.querySelectorAll('.om-button:not(.om-button-fallback)');

    return (
      [...buttons].filter((button) => events.includes(button.OMButton.settings.action)).length === 0
    );
  }

  hasCampaignInput() {
    const campaign = this.getCampaignElement();
    if (!campaign) return false;
    return campaign.querySelectorAll('input').length > 0;
  }

  addImageLinkListener() {
    OptiMonk.addListener(this.getIFrameElement(), 'click', ImageWithRedirect.handleClick);
  }

  addCartUpdateListener() {
    OptiMonk.addListener(
      document.querySelector('html'),
      'optimonk#cart-product-types-changed',
      this.initProducts.bind(this, { reInit: true }),
    );
  }
}
