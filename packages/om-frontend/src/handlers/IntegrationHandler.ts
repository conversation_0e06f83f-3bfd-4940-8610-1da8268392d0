import { ObjectId } from 'mongodb';
import { pino } from '../helpers/logger';
import delayedSubscriptions from '../queues/delayedSubscriptions';
import HandlingResult from './conversions/HandlingResult';
import IntegrationResponse from './conversions/IntegrationResponse';
import { ValidationResults } from './conversions/integrations/IIntegrationAdapter';
import IntegrationFactory from './conversions/integrations/IntegrationFactory';

const log = pino.child({ service: 'IntegrationHandler' });

export type VariantFields = {
  _id: ObjectId;
  status: 'active' | 'inactive' | 'deleted';
  fields: {
    name: string;
    type: string;
    customId: string;
  }[];
};
export type CampaignVariantFields = VariantFields[];

export default class IntegrationHandler {
  public static async handle(
    databaseId,
    campaignId,
    variantId,
    subscriber,
    body,
    globalSettings,
    specificIntegrationSetting,
    campaignVariantFields: CampaignVariantFields,
    hasStopOnMissingReqFieldFlag = false,
  ) {
    const integrationSettings = { global: globalSettings, specific: specificIntegrationSetting };
    const integration = IntegrationFactory.create(integrationSettings, databaseId, body);
    if (integration) {
      const variantFields = campaignVariantFields.find(({ _id }) => _id.equals(variantId));

      if (variantFields) {
        const validationResult = integration.canHandleVariant(variantFields, body);
        if (validationResult !== ValidationResults.Valid) {
          // Current variant cannot handle this subscription we check other active variants.
          const otherVariantCanHandle = campaignVariantFields.some((variantFields) => {
            const { _id, status } = variantFields;

            return (
              !_id.equals(variantId) &&
              status === 'active' &&
              integration.canHandleVariant(variantFields, body)
            );
          });

          if (otherVariantCanHandle) {
            log.info(
              `${
                globalSettings.type
              } [${validationResult}] integration can handle other active variant, not sending it: databaseId = ${databaseId}, campaignId = ${campaignId}, variantId = ${variantId}, body = ${JSON.stringify(
                body,
              )}`,
            );
            // There is another active variant which could handle this subscription
            // we skip sending it to this integration in order to not generate "false" integration error.
            return null;
          }

          log.info(
            `${
              globalSettings.type
            } [${validationResult}] integration cannot handle any of the active variants, sending it: databaseId = ${databaseId}, campaignId = ${campaignId}, variantId = ${variantId}, body = ${JSON.stringify(
              body,
            )}`,
          );

          if (hasStopOnMissingReqFieldFlag) {
            return null;
          }
        }
      }

      if (integration.delayEnabled) {
        await delayedSubscriptions.removeDelayedJob(databaseId, variantId, subscriber.pageUserId);
        const needToSendRightNow = subscriber.__META__?.syncToIntegration === 'rightNow';

        if (!subscriber.__META__?.allInputFilled && !needToSendRightNow) {
          return new HandlingResult(
            integration.settings,
            new IntegrationResponse(200, 'Delayed', ''),
            subscriber,
            null,
            true,
          );
        }
      }

      log.info({
        message: `Subscription send immediate for: ${databaseId} integration type: ${integration.settings.getType()}`,
        databaseId,
        variantId,
        body,
        type: integration.settings.getType(),
      });

      return await integration.handle(campaignId, subscriber);
    }

    return null;
  }
}
