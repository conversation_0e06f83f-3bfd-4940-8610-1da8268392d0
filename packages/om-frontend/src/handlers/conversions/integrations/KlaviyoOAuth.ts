/* eslint-disable camelcase */
import { ObjectId } from 'mongodb';
import { stringify } from 'qs';
import axios, { AxiosRequestConfig } from 'axios';
import IIntegrationAdapter from './IIntegrationAdapter';
import { ISubscriber } from '../../AbstractConversionHandler';
import HandlingResult from '../HandlingResult';
import IntegrationResponse from '../IntegrationResponse';
import AbstractOAuthIntegration from './AbstractOAuthIntegration';
import { isFeatureEnabled, FEATURES } from '../../../helpers/featureFlags';
import KlaviyoInstantConsent from './klaviyo/KlaviyoInstantConsent';
import KlaviyoBulkSubscribe from './klaviyo/KlaviyoBulkSubscribe';
import KlaviyoOAuthProvider from './klaviyo/KlaviyoOAuthProvider';

const CLIENT_ID = 'fc5b62d5-e7f8-47a9-8803-b114a4dbfd16';
const CLIENT_SECRET =
  'wXmjS2loK5GMj-uLUm4zD9vz6Dd-SsL10Ovlxxi1b-bM3BV7uVAe9lPTKSLQjBZlBY-ZbP9skAo_IWdHYWpghg';

export default class KlaviyoOAuth extends AbstractOAuthIntegration implements IIntegrationAdapter {
  private accessToken: string = '';
  private PHONE_NUMBER = 'phone_number';

  getType() {
    return 'klaviyoOAuth';
  }

  protected async getAccessToken() {
    let token;
    const isTokenExpired = await this.isTokenExpired();
    if (isTokenExpired) {
      try {
        token = await this.refreshToken();
      } catch (e) {
        const errorMessage = e.response && e.response.data ? e.response.data : e.message;
        this.logWarn('tokenRefreshError', {
          received: errorMessage,
        });
      }
    } else {
      token = this.settings.getGlobal('access_token');
    }

    return token;
  }

  protected async isTokenExpired(): Promise<boolean> {
    const expiresAt = new Date(this.settings.getGlobal('expires_at'));
    const currentDatePlus10Mins = new Date();
    currentDatePlus10Mins.setMinutes(currentDatePlus10Mins.getMinutes() + 10);

    return currentDatePlus10Mins >= expiresAt;
  }

  protected _getBasicAuthorizationHeader() {
    return `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`;
  }

  protected async refreshToken(): Promise<string> {
    try {
      const response = await axios.post(
        'https://a.klaviyo.com/oauth/token',
        stringify({
          grant_type: 'refresh_token',
          refresh_token: this.settings.getGlobal('refresh_token'),
        }),
        {
          headers: {
            Authorization: this._getBasicAuthorizationHeader(),
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + response.data.expires_in);

      const oldData = {
        access_token: this.settings.getGlobal('access_token'),
      };

      const newData = {
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
        expires_at: expiresAt,
      };

      await this.saveToken(oldData, newData);

      return response.data.access_token;
    } catch (e) {
      this.logError('Error refreshing Klaviyo OAuth token', e);
      throw e;
    }
  }

  protected getPhoneNumberFromBindings(subscriber: ISubscriber) {
    const bindings = this.settings.getSpecific('bindings');
    const phoneBinding = bindings.find((binding) => binding.externalId === this.PHONE_NUMBER);
    return phoneBinding && subscriber.customFields
      ? subscriber.customFields[phoneBinding.fieldId]
      : '';
  }

  setMultiListId(email: string, phoneNumber: string) {
    const hasMultiList = this.settings.getSpecific('multiList');
    if (!hasMultiList) return;

    if (email && phoneNumber) {
      this.settings.setSpecific('listId', hasMultiList.emailAndPhone);
      return;
    }

    if (email) {
      this.settings.setSpecific('listId', hasMultiList.emailOnly);
      return;
    }

    this.settings.setSpecific('listId', hasMultiList.phoneOnly);
  }

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    const phoneNumber = this.getPhoneNumberFromBindings(subscriber);

    // handle multi-list
    this.setMultiListId(subscriber.email, phoneNumber);

    if (!subscriber.email && !phoneNumber) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    // Get the OAuth token
    this.accessToken = await this.getAccessToken();
    if (!this.accessToken) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(401, 'Unauthorized', 'Failed to get access token'),
        subscriber,
      );
    }

    // Set the token in the settings for the handlers to use
    this.settings.setGlobal('accessToken', this.accessToken);

    if (
      this.settings.getGlobal('publicApiKey') &&
      (await isFeatureEnabled(this.databaseId, FEATURES.KLAVIYO_NEW_SYNCHRONOUS_API, true))
    ) {
      const klaviyoInstantConsent = new KlaviyoInstantConsent(
        this.settings,
        this.databaseId,
        this.body,
        new KlaviyoOAuthProvider(this.settings),
      );

      return klaviyoInstantConsent.handle(campaignId, subscriber);
    }

    const klaviyoBulkSubscribe = new KlaviyoBulkSubscribe(
      this.settings,
      this.databaseId,
      this.body,
      new KlaviyoOAuthProvider(this.settings),
    );

    return klaviyoBulkSubscribe.handle(campaignId, subscriber);
  }

  protected buildPayload(subscriber: ISubscriber): any {}

  protected getAxiosConfig(): AxiosRequestConfig | undefined {
    return undefined;
  }

  protected getSubscribeConfig(subscriber): { url: string; method: string; [p: string]: any } {
    return { method: '', url: '' };
  }
}
