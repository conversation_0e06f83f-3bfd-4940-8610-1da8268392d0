import { ISubscriber } from '../../../AbstractConversionHandler';
import BindingHelper from './bindingHelper';
import { API_VERSION, PARTNER_KEY } from './config';
import { IPayload } from './types';
import AbstractKlaviyoIntegration from './AbstractKlaviyoIntegration';

export default class KlaviyoInstantConsent extends AbstractKlaviyoIntegration {
  private lastConfig?: { data?: any };

  protected async prepareSubscription(subscriber: ISubscriber): Promise<void> {
    const config = this.getSubscribeConfig(subscriber);
    this.lastConfig = { data: config.data };
  }

  protected getLastConfig(): { data?: any } | undefined {
    return this.lastConfig;
  }

  buildPayload(subscriber: ISubscriber) {
    const profile: any = {};
    const bindings = this.settings.getSpecific('bindings');

    if (Array.isArray(bindings)) {
      BindingHelper.setBindings({
        body: this.body,
        campaignData: this.campaignData,
        subscriber,
        bindings,
        properties: profile,
      });
    }

    const payload: IPayload = {
      data: {
        type: 'subscription',
        attributes: {
          profile: {
            data: {
              type: 'profile',
              attributes: profile,
            },
          },
        },
      },
    };

    const listId = this.settings.getSpecific('listId');
    if (listId) {
      payload.data.relationships = {
        list: {
          data: {
            type: 'list',
            id: listId,
          },
        },
      };
    }

    return payload;
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    return {
      baseURL: 'https://a.klaviyo.com/',
      headers: {
        revision: API_VERSION,
        'Content-Type': 'application/json',
        'x-klaviyo-partner-key': PARTNER_KEY,
      },
    };
  }

  protected getSubscribeConfig(subscriber: ISubscriber): {
    [key: string]: any;
    url: string;
    method: string;
    data: any;
  } {
    return {
      method: 'post',
      url: `/client/subscriptions?company_id=${this.settings.getGlobal('publicApiKey')}`,
      data: this.buildPayload(subscriber),
    };
  }
}
