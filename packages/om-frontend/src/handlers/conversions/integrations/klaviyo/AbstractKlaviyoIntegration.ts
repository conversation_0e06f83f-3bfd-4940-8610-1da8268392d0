import { ObjectId } from 'mongodb';
import axios from 'axios';
import { ISubscriber } from '../../../AbstractConversionHandler';
import AbstractIntegration from '../AbstractIntegration';
import HandlingResult from '../../HandlingResult';
import IIntegrationAdapter from '../IIntegrationAdapter';
import IntegrationResponse from '../../IntegrationResponse';
import IntegrationSettings from '../../settings/IntegrationSettings';
import { AbstractKlaviyoAuthProvider } from './AbstractKlaviyoAuthProvider';

export default abstract class AbstractKlaviyoIntegration
  extends AbstractIntegration
  implements IIntegrationAdapter
{
  protected authProvider: AbstractKlaviyoAuthProvider | undefined;
  getType() {
    return 'klaviyo';
  }

  constructor(
    settings: IntegrationSettings,
    databaseId: number,
    body: any,
    authProvider?: AbstractKlaviyoAuthProvider,
  ) {
    super(settings, databaseId, body);
    this.authProvider = authProvider;
  }

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);

    if (await this.isOverLimit(subscriber)) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    const response = await this.subscribe(subscriber);
    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async subscribe(subscriber: ISubscriber): Promise<IntegrationResponse> {
    const integResponses: Array<{ response: IntegrationResponse; success: boolean }> = [];

    try {
      await this.prepareSubscription(subscriber);
      const config = this.getSubscribeConfig(subscriber);
      this.logInfo(`Klaviyo subscription, databaseId: ${this.databaseId}`, config);

      this.axiosInstance = axios.create(this.getAxiosConfig());
      const response = await this.axiosInstance.request(config);
      integResponses.push({
        response: new IntegrationResponse(
          response.status,
          response.statusText,
          config.data,
          response,
        ),
        success: true,
      });
    } catch (e: any) {
      this.logWarn('SUBSCRIBE EXCEPTION', e.message);
      const config = this.getLastConfig();
      integResponses.push({
        response: new IntegrationResponse(
          e.response?.status,
          e.response?.statusText,
          config?.data || {},
          e.response,
        ),
        success: false,
      });
    }

    const failedIndex = integResponses.findIndex((r) => !r.success);
    const integrationResponse =
      failedIndex !== -1
        ? integResponses[failedIndex].response
        : integResponses.filter((r) => r.success)[0].response;
    return integrationResponse;
  }

  protected abstract getAxiosConfig(): { [key: string]: any; baseURL: string };

  protected abstract getSubscribeConfig(subscriber?: ISubscriber): {
    [key: string]: any;
    url: string;
    method: string;
    data?: any;
  };

  protected abstract prepareSubscription(subscriber: ISubscriber): Promise<void>;

  protected abstract getLastConfig(): { data?: any } | undefined;
}
