import RedisLock = require('ioredis-lock');
import { client as redisClient } from './ioRedisAdapter';
import { pino as logger } from './logger';
import { getAccountsWithFeatureFlag } from './mongoHelper';

export const FEATURES = {
  // region Can be deleted
  MEASURE_LOAD_TIME: 'MEASURE_LOAD_TIME',
  JF_IDB_EVENT_STORE: 'JF_IDB_EVENT_STORE',
  MEASURE_EMBEDDED_IMPRESSION: 'MEASURE_EMBEDDED_IMPRESSION',
  // endregion
  STOP_ON_INTEGRATION_ERROR: 'STOP_ON_INTEGRATION_ERROR',
  FRONTEND_USE_CDN_STATIC: 'FRONTEND_USE_CDN_STATIC',
  NOTIFY_ON_ERROR_SUB: 'NOTIFY_ON_ERROR_SUB',
  SHADOW_CAMPAIGNS: 'SHADOW_CAMPAIGNS',
  SSR_FEATURE_FLAG: 'SERVER_SIDE_RENDERING',
  FRONTEND_NO_MODULE: 'FRONTEND_NO_MODULE',
  BUNNY_FONTS: 'BUNNY_FONTS',
  OPTIMIZED_SELECTED_VARIANTS_QUERY: 'OPTIMIZED_SELECTED_VARIANTS_QUERY',
  TRACK_JS: 'TRACK_JS',
  SMART_FONT_LOADER: 'SMART_FONT_LOADER',
  SMART_PRODUCT_TAG: 'SMART_PRODUCT_TAG',
  SMART_PAGE_TAG: 'SMART_PAGE_TAG',
  SPPO_RESEARCH_CDN: 'SPPO_RESEARCH_CDN',
  KLAVIYO_NEW_SYNCHRONOUS_API: 'KLAVIYO_NEW_SYNCHRONOUS_API',
  DISCOUNT_CODE_ASYNC: 'DISCOUNT_CODE_ASYNC',
  CURRENT_URL_2024: 'CURRENT_URL_2024',
  VISITOR_CART_V3: 'VISITOR_CART_V3',
  VARIANT_TEMPLATE_MIGRATED: 'VARIANT_TEMPLATE_MIGRATED',
  BLOCK_LINUX_VISITORS: 'BLOCK_LINUX_VISITORS',
  FREQUENCY_RULE_V2: 'FREQUENCY_RULE_V2',
  FAT_INSERT_SCRIPT: 'FAT_INSERT_SCRIPT',
  STOP_ON_MISSING_REQUIRED_FIELD: 'STOP_ON_MISSING_REQUIRED_FIELD',
};

const REDIS_BATCH_SIZE = 50;
const REDIS_LOCK_TIMEOUT = 10000;

const makeFeatureFlagAccountKey = (flag: string) => `feature:${flag}:accountIds`;

export const isFeatureEnabled = async (
  accountId: string | number,
  flag: string,
  autoLoad: { ttl?: number; await?: boolean } | boolean = false,
) => {
  const key = makeFeatureFlagAccountKey(flag);

  const isEnabledForAll = await redisClient.sismember(key, '*');
  if (isEnabledForAll === 1) {
    return true;
  }

  const [ttlResponse, sismemberResponse] = await redisClient
    .pipeline()
    .ttl(key)
    .sismember(key, `${accountId}`)
    .exec();

  const ttl = ttlResponse && ttlResponse[1];
  const exists = ttl > 0 || ttl === -1;
  const isMember = sismemberResponse && sismemberResponse[1];

  if (autoLoad && ttl < 60 && ttl !== -1) {
    // Redis Set is about to expire or it does not exists and auto-load was requested
    const loadTtl = typeof autoLoad !== 'boolean' ? autoLoad.ttl : undefined;
    const awaitPromise = typeof autoLoad !== 'boolean' ? autoLoad.await : false;
    const promise = cacheLoadFeatureAccounts(flag, loadTtl);

    if (!exists && awaitPromise) {
      // Only await the cache load if the Set does not exist
      const loaded = await promise;

      if (loaded) {
        // Some data was loaded into the cache, we can re-call ourself so cache reading will happen again
        // (after the fresh cache load) but this time we disable auto-load on purpose.
        return isFeatureEnabled(accountId, flag, false);
      }
    }
  }

  return isMember === 1;
};

export const cacheLoadFeatureAccounts = async (flag: string, ttl = 3600) => {
  const lock = RedisLock.createLock(redisClient, { timeout: REDIS_LOCK_TIMEOUT });
  let batch: number[] = [];
  let loaded = 0;
  const key = makeFeatureFlagAccountKey(flag);
  const tmpKey = `${key}:tmp`;

  try {
    await lock.acquire(`${key}:lock`);
    const startTs = Date.now();

    // Clean-up partial temporary Redis Set left by previous cache load attempt which failed.
    await redisClient.del(tmpKey);

    for await (const account of getAccountsWithFeatureFlag(flag)) {
      batch.push(account.databaseId);
      if (batch.length >= REDIS_BATCH_SIZE) {
        // We call SADD command with smaller groups of members since it is an atomic blocking operation
        // This way Redis response latency wont spike while we load our data into the cache.
        await redisClient.sadd(tmpKey, ...batch);
        batch = [];
      }

      loaded++;
    }

    if (batch.length > 0) {
      // Flush last partial batch to Redis
      await redisClient.sadd(tmpKey, ...batch);
    }

    const pipeline = redisClient.pipeline();
    if (loaded === 0) {
      // No account members were added to the Redis Set so we add a "fake" one.
      // This way EXISTS command can be used on the Set.
      pipeline.sadd(tmpKey, 'placeholder');
    }

    await pipeline.expire(tmpKey, ttl).rename(tmpKey, key).exec();

    logger.info('Feature flag "%s" loaded into cache: %o', flag, {
      loaded,
      took: Date.now() - startTs,
    });

    return loaded > 0;
  } catch (err) {
    if (!(err instanceof RedisLock.LockAcquisitionError)) {
      logger.error({
        message: `An error occurred while loading account ids with feature flag "${flag}": ${err.message}`,
        err,
        stack: err.stack,
      });
    }
    return false;
  } finally {
    if (lock && lock._locked) {
      lock.release();
    }
  }
};
