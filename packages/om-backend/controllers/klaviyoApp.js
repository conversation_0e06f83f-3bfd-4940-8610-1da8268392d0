const express = require('express');
const pino = require('../logger');
const { tokenHeader } = require('../util/jwt');

const logger = pino.child({ service: 'klaviyo-app-install' });
const FRONTEND_URL = process.env.om_new_admin_url;
const KLAVIYO_COOKIE_KEY = 'x-om-klaviyo-oauth';

const router = express.Router();

router.get('/', async (req, res) => {
  const { userId } = await tokenHeader(req);
  logger.info({ message: 'Klaviyo app install flow started' });

  const ONE_HOUR = 60 * 60 * 1000;
  res.cookie(KLAVIYO_COOKIE_KEY, 1, { httpOnly: true, maxAge: ONE_HOUR });
  if (!userId) {
    res.redirect(`${FRONTEND_URL}/register`);
    return;
  }

  res.redirect(`${FRONTEND_URL}/login`);
});

module.exports = { router, KLAVIYO_COOKIE_KEY };
