const express = require('express');

const router = express.Router();
const { tokenHeader } = require('../util/jwt');
const mailUpHandler = require('./oauth/mailUp');
const automizyHandler = require('./oauth/automizy');
const aWeberHandler = require('./oauth/aWeber');
const cleverReachHandler = require('./oauth/cleverReach');
const copernicaHandler = require('./oauth/copernica');
const KeapHandler = require('./oauth/keap');
const verticalResponseHandler = require('./oauth/verticalResponse');
const salesforceHandler = require('./oauth/salesforce');
const smsBumpHandler = require('./oauth/smsBump');
const acerCCDBHandler = require('./oauth/acerCCDBV2');
const hubSpotV2Handler = require('./oauth/hubSpotV2');
const highLevelHandler = require('./oauth/highLevel');
const klaviyoOAuthHandler = require('./oauth/klaviyoOAuth');

router.use('/mailUp', mailUpHandler);
router.use('/automizy', automizyHandler);
router.use('/aWeber', aWeberHandler);
router.use('/cleverReach', cleverReachHandler);
router.use('/copernica', copernicaHandler);
router.use('/keap', KeapHandler);
router.use('/verticalResponse', verticalResponseHandler);
router.use('/salesforce', salesforceHandler);
router.use('/smsBump', smsBumpHandler);
router.use('/acerCCDB', acerCCDBHandler);
router.use('/hubSpotV2', hubSpotV2Handler);
router.use('/highLevel', highLevelHandler);
router.use('/klaviyoOAuth', klaviyoOAuthHandler);

router.get('/', async (req, res) => {
  req.log.info('integration o-auth endpoint reached');
  const { userId } = await tokenHeader(req);

  if (!userId) {
    res.sendStatus(401);
  }

  req.log.info({ params: req.params, body: req.body });
});

module.exports = router;
