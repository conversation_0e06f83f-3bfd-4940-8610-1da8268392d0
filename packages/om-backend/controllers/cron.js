const express = require('express');

const router = express.Router();
const _difference = require('lodash.difference');
const moment = require('moment');
const { model: AccountModel } = require('../resources/account/account.model');
const { model: LoginModel } = require('../resources/login/login.model');
const { model: ActiveAccountsModel } = require('../models/active_accounts.model');
const { model: campaignModel, ObjectId } = require('../resources/campaign/campaign.model');
const { userModel: imageModel } = require('../resources/image/image.model');
const { model: TemplateModel } = require('../resources/template/template.model');
const { model: SubscriberModel } = require('../resources/subscriber/subscriber.model');
const { model: StatsModel } = require('../resources/statistics/statistics.model');
const { model: BackofficeCsm } = require('../resources/bo_csm/bo_csm.model');
const { model: VariantTemplateModel } = require('../resources/campaign/variantTemplate.model');

const { FEATURES, isFeatureEnabled } = require('../util/feature');
const { regenerateTemplateIds } = require('../services/campaignUtil');
const {
  uploadAndSavePreviewsForTemplate,
  isGamificationTemplate,
  isSurveyTemplate,
} = require('../util/templateUtil');
const DDS = require('../services/dds/dds');
const DDSQueries = require('../services/dds/queries');
const failedSubscriptions = require('../services/failedSubscriptions');
const interCron = require('../services/interCron');
const qualifiedLeadCron = require('../services/qualifiedLeadCron');
const { runScheduleIfNeeded } = require('../services/mixpanel');
const redis = require('../services/ioRedisAdapter');
const redisClient = require('../services/ioRedisAdapter');
const S3 = require('../services/s3');
const shopifySql = require('../services/shopifyQualificationHelper');
const templateService = require('../services/template');
const useCaseService = require('../services/useCase');
const fraudPayment = require('../services/fraudPayment');

const limitNotifier = require('../services/mail/limitNotifier');
const heapAnalytics = require('../services/heapAnalytics');
const slack = require('../services/slack');
const { subscriberForDemo } = require('../scripts/fake/subscriber');
const Mailer = require('../util/mailer');
const { REGIONS } = require('../config/regions');
const { regex: midmarketPackages } = require('../config/midmarket_packages');
const { handleShopInstall } = require('../services/jetfabric');
const { slugify } = require('../util/string');
const salesMysql = require('../util/salesMysql');
const { downloadTemplates, uploadTemplates } = require('../helpers/templateSync');
const { updateAccountCampaignInfo } = require('../helpers/account/campaigns');
const {
  updateServiceIntegrationsMaterializedView,
  deleteRemovedShops,
} = require('../helpers/serviceIntegrationMaterialisedView');
const TemplateTranslator = require('../services/templateTranslator/TemplateTranslator');
const { updateTemplateFuzzySearch } = require('../resources/template_fuzzy_search/helpers');
const { SPPOAutoGenerate } = require('../services/sppoGenerate/sppoAutoGenerate');
const { SPPOAutoPublish } = require('../services/sppoGenerate/sppoAutoPublish');
const { SPPOAutoGenerateLogger } = require('../services/sppoGenerate/logger');
const { SmartABTestEvaluator } = require('../services/smartABTest/evaluation/SmartABTestEvaluator');
const { getChangesForEvaluation } = require('../services/smartABTest/evaluation/mongoHelper');
const { getSPRList, triggerEmbedding } = require('../services/spr/sprEmbedding');
const userAlertEmailScheduler = require('../services/userAlert/userAlertEmailScheduler');
const currentUrlRuleMigrator = require('../services/currentUrlRuleMigration/migrator');
const currentUrlRuleGlobalSegmentMigrator = require('../services/currentUrlRuleMigration/globalSegmentMigrator');
const visitorCartMigrator = require('../scripts/cartRuleMigration');
const {
  run: runVariantTemplateMigration,
  revert: revertVariantTemplateMigration,
} = require('../scripts/variantTemplate');
const frequencyRuleMigrator = require('../scripts/frequencyRuleV2Migration');
const { detectNonstandardOrderShops } = require('../services/nonstandardOrders');
const { delKeysWithScan } = require('../services/ioRedisAdapter');
const { queueCampaignReport, getMailList } = require('../services/queue/campaignReportQueue');
const { triggerProductSyncFlow, getProductFeedSyncList } = require('../services/productFeed/sync');
const { getDDSConnectionHandler } = require('../util/ddsConnection');
const siteDataGenerator = require('../resources/site_extraction/site_extraction.resolvers');
const autoThemeGenerator = require('../resources/auto_theme/auto_theme.resolvers');

const ONE_MINUTE = 60 * 1000;

let isIntercomBusy = false;
let isLeadSyncBusy = false;
let isMixpanelBusy = false;
let isShopifySqlBusy = false;

let isResendFailedSubsBusy = false;
let isVisitorLimitBusy = false;
let isDDSBusy = false;
let isTemplateBechmarksBusy = false;
let isHeapBusy = false;
let isHeapOrdersBusy = false;
let isSlackOrdersBusy = false;
let isSlackNewAgencyBusy = false;
let isSlackNewMidmarketBusy = false;
let isSlackMidmarketUpgradeBusy = false;
let isSlackNewAffiliateBusy = false;
let isSlackAffiliateLevelUpBusy = false;
let isSlackBigMoneyLimitReachedPingBusy = false;
let isSlackHu80LimitReachedPingBusy = false;
let isSlackFreemiumHotLeadsPingBusy = false;
let isFakeSubscriberForDemoBusy = false;
let isJFCredentialSyncBusy = false;
let isFuzzySearchBusy = false;

router.use(async (req, res, next) => {
  if (req.query.cron_token === process.env.cron_token) {
    req.setTimeout(10 * ONE_MINUTE);
    next();
  } else {
    res.sendStatus(401);
  }
});

const validateUserIds = async (req, res, next) => {
  let userIds = req.query.userIds;
  if (userIds) {
    try {
      req.userIds = JSON.parse(userIds);
      next();
    } catch (e) {
      res.status(422).send('userIds are required');
    }
  } else {
    res.status(422).send('userIds are required');
  }
};

const validateFeature = async (req, res, next) => {
  let feature = req.query.feature;

  if (feature) {
    if (FEATURES[feature.toUpperCase()]) {
      req.feature = feature.toUpperCase();
      next();
    } else {
      res.status(422).send('feature param is not valid!');
    }
  } else {
    res.status(422).send('feature param required!');
  }
};

router.get('/shopify-sql', async (req, res) => {
  if (!isShopifySqlBusy) {
    try {
      isShopifySqlBusy = true;
      const response = await shopifySql.updateLatest();

      res.send(response);
    } catch (err) {
      req.log.error(err, 'Error while running shopify-sql cron task');
      res.status(500).json({ success: false });
    } finally {
      isShopifySqlBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/fraud-reset/:ip', async (req, res) => {
  const { ip } = req.params;

  if (!ip) {
    return res.json({ success: 'NOK' });
  }

  try {
    await fraudPayment.removeFromRedis(req.params.ip);

    res.json({ success: 'OK' });
  } catch (err) {
    res.status(500).json({ success: false });
  }
});

router.get('/intercom', async (req, res) => {
  if (!isIntercomBusy) {
    try {
      isIntercomBusy = true;
      let response = await interCron(req.query.databaseId);
      res.send(response);
    } catch (err) {
      req.log.error(err, 'Error while running intercom cron task');
      res.status(500).json({ success: false });
    } finally {
      isIntercomBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/lead-sync', async (req, res) => {
  if (isLeadSyncBusy) {
    res.status(422).send({ busy: true });
    return;
  }
  try {
    isLeadSyncBusy = true;
    let response = await qualifiedLeadCron();
    res.send(response);
  } catch (err) {
    req.log.error(err, 'Error while running lead-sync cron task');
    res.status(500).json({ success: false });
  } finally {
    isLeadSyncBusy = false;
  }
});

router.get('/generate-fake-demo', async (req, res) => {
  if (!isFakeSubscriberForDemoBusy) {
    try {
      isFakeSubscriberForDemoBusy = true;
      let response = await subscriberForDemo({
        conversionsInterval: { min: 1, max: 100 },
      });
      res.send(response);
    } catch (err) {
      req.log.error(err, 'Error while running fake demo subscriber generate cron task');
      res.status(500).json({ success: false });
    } finally {
      isFakeSubscriberForDemoBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/mixpanel', async (req, res) => {
  if (!isMixpanelBusy) {
    try {
      isMixpanelBusy = true;
      let response = await runScheduleIfNeeded();
      res.send(response);
    } catch (err) {
      req.log.error(err, 'Error while running mixpanel cron task');
      res.status(500).json({ success: false });
    } finally {
      isMixpanelBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/heap', async (req, res) => {
  if (!isHeapBusy) {
    try {
      isHeapBusy = true;
      let days = 5;
      if (req.query.days) days = parseInt(req.query.days, 10);
      req.log.info(`startHeap days: ${days}`);
      heapAnalytics.syncFromDDS(days);
      req.log.info('finishHeap');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running heap cron task');
      res.status(500).json({ success: false });
    } finally {
      isHeapBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/heap-orders', async (req, res) => {
  if (!isHeapOrdersBusy) {
    try {
      isHeapOrdersBusy = true;
      req.log.info('startHeapOrders');
      heapAnalytics.syncOrders();
      req.log.info('finishHeapOrders');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running heap cron task');
      res.status(500).json({ success: false });
    } finally {
      isHeapOrdersBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-orders', async (req, res) => {
  if (!isSlackOrdersBusy) {
    try {
      isSlackOrdersBusy = true;
      req.log.info('startSlackOrders');
      await slack.sendFirstOrders();
      req.log.info('finishSlackOrders');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack orders cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackOrdersBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-new-agency', async (req, res) => {
  if (!isSlackNewAgencyBusy) {
    try {
      isSlackNewAgencyBusy = true;
      req.log.info('startSlackNewAgency');
      await slack.sendAgencyLead();
      req.log.info('finishSlackNewAgency');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack new agency cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackNewAgencyBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-new-midmarket', async (req, res) => {
  if (!isSlackNewMidmarketBusy) {
    try {
      isSlackNewMidmarketBusy = true;
      req.log.info('startSlackNewMidmarketLead');
      await slack.sendMidmarketLeads();
      req.log.info('finishSlackNewMidmarketLead');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack new midmarket lead cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackNewMidmarketBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-midmarket-upgrade', async (req, res) => {
  if (!isSlackMidmarketUpgradeBusy) {
    try {
      isSlackMidmarketUpgradeBusy = true;
      req.log.info('startSlackMidmarketUpgradeLead');
      await slack.sendMidmarketUpgrades();
      req.log.info('finishSlackMidmarketUpgradeLead');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack midmarket upgrade lead cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackMidmarketUpgradeBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-new-affiliate', async (req, res) => {
  if (!isSlackNewAffiliateBusy) {
    try {
      isSlackNewAffiliateBusy = true;
      req.log.info('startSlackNewAffiliate');
      await slack.sendNewAffiliates();
      req.log.info('finishSlackNewAffiliate');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack new affiliate cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackNewAffiliateBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-affiliate-level-up', async (req, res) => {
  if (!isSlackAffiliateLevelUpBusy) {
    try {
      isSlackAffiliateLevelUpBusy = true;
      req.log.info('startSlackAffiliateLevelUpPing');
      await slack.sendAffiliateLevelUpPing();
      req.log.info('finishSlackAffiliateLevelUpPing');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slack affiliate level up ping cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackAffiliateLevelUpBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-big-money-limit-reached-sales-ping', async (req, res) => {
  if (!isSlackBigMoneyLimitReachedPingBusy) {
    try {
      isSlackBigMoneyLimitReachedPingBusy = true;
      req.log.info('startSlackBigMoneyLimitReachedPing');
      await slack.sendBigMoneyLimitReachedPing();
      req.log.info('finishSlackBigMoneyLimitReachedPing');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slackBigMoneyLimitReachedPing cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackBigMoneyLimitReachedPingBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-hu-80-limit-reached-sales-ping', async (req, res) => {
  if (!isSlackHu80LimitReachedPingBusy) {
    try {
      isSlackHu80LimitReachedPingBusy = true;
      req.log.info('startSlackHu80LimitReachedPing');
      await slack.sendHu80LimitReached();
      req.log.info('finishSlackHu80LimitReachedPing');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slackHu80LimitReachedPing cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackHu80LimitReachedPingBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-freemium-hot-leads-ping', async (req, res) => {
  if (!isSlackFreemiumHotLeadsPingBusy) {
    try {
      isSlackFreemiumHotLeadsPingBusy = true;
      req.log.info('startSlackFreemiumHotLeadsPing');
      await slack.sendFreemiumHotLeads();
      req.log.info('finishSlackFreemiumHotLeadsPing');
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running slackSlackFreemiumHotLeadsPing cron task');
      res.status(500).json({ success: false });
    } finally {
      isSlackFreemiumHotLeadsPingBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/slack-ideal-shopify-leads-ping', async (req, res) => {
  try {
    req.log.info('startIdealShopifyLeadsPing');
    await slack.sendIdealShopifyLeads();
    req.log.info('finishstartIdealShopifyLeadsPing');
    res.send('Done');
  } catch (err) {
    req.log.error(err, 'Error while running sendIdealShopifyLeads cron task');
    res.status(500).json({ success: false });
  }
});

router.get('/dds', async (req, res) => {
  if (!isDDSBusy) {
    try {
      isDDSBusy = true;
      const userId = req.query.userId ? parseInt(req.query.userId, 10) : null;
      DDS.run(userId);
      return res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running dds cron task');
      return res.status(500).json({ success: false });
    }
  } else {
    return res.status(422).send({ busy: true });
  }
});

router.get('/refresh-template-benchmarks', async (req, res) => {
  if (!isTemplateBechmarksBusy) {
    try {
      isTemplateBechmarksBusy = true;
      req.log.info('cron templateBenchmarkRefresh start');
      const benchmarks = await DDSQueries.getTemplateBenchmarks();
      const updates = benchmarks.map((e) => {
        return {
          updateOne: {
            filter: { _id: e._id },
            update: {
              $set: {
                conversionRate: e.conversionRate,
                popularity: e.count,
              },
            },
          },
        };
      });
      let modified = 0;
      if (updates.length > 0) {
        const bwResult = await TemplateModel.bulkWrite(updates);
        modified = bwResult.nModified;
      }
      req.log.info(`cron templateBenchmarkRefresh finish modified: ${modified}`);
      res.send('Done');
    } catch (err) {
      req.log.error(err, 'Error while running refresh-template-benchmarks cron task');
      res.status(500).json({ success: false });
    } finally {
      isTemplateBechmarksBusy = false;
    }
  } else {
    res.status(422).send({ busy: true });
  }
});

router.get('/active-accounts-stat', async (req, res) => {
  req.log.info('-cron- active-accounts-stat start');
  await ActiveAccountsModel.updateActiveAccountsStat();
  req.log.info('-cron- active-accounts-stat end');
  res.send({ success: true });
});

router.get('/limit-notifier', async (req, res) => {
  if (isVisitorLimitBusy) {
    return res.status(422).json({ busy: true });
  }

  try {
    isVisitorLimitBusy = true;
    await limitNotifier();
    res.send('Done');
  } catch (err) {
    req.log.error(err, 'Error while running visitor-limit cron task');
    res.status(500).json({ success: false });
  } finally {
    isVisitorLimitBusy = false;
  }
});

router.get('/resend-failed-subscriptions', async (req, res) => {
  if (isResendFailedSubsBusy) {
    return res.status(422).json({ busy: true });
  }

  try {
    isResendFailedSubsBusy = true;
    await failedSubscriptions.resend();
    res.send('Done');
  } catch (err) {
    req.log.error(err, 'Error while running resend-failed-subscriptions cron task');
    res.status(500).json({ success: false });
  } finally {
    isResendFailedSubsBusy = false;
  }
});

router.get('/delete-redis', async (req, res) => {
  const accounts = await AccountModel.find({}, { databaseId: 1 });
  const userIds = accounts.map((a) => a.databaseId);
  for (let userId of userIds) {
    const keyPrefix = `user:${userId}:campaign:active:`;
    await Promise.all([redis.del(`${keyPrefix}mobile`), redis.del(`${keyPrefix}desktop`)]);
  }
  req.log.info('-cron- deleted redis cache for %d users:', userIds.length);
  res.send({ success: true });
});

router.get('/add-feature', [validateUserIds, validateFeature], async (req, res) => {
  for (let userId of req.userIds) {
    let account = await AccountModel.findOne({ databaseId: userId });

    if (!account) {
      continue;
    }

    let featureIndex = account.features.indexOf(req.feature);
    // remove frontend cache
    if (redisClient.inited()) {
      req.log.info('deleting redis user cache for user id =', userId);
      const keyPrefix = `user:${userId}:campaign:active:`;
      let redisResults = await Promise.all([
        redisClient.del(`${keyPrefix}mobile`),
        redisClient.del(`${keyPrefix}desktop`),
      ]);
      req.log.info('deleted redis user cache %o', redisResults);
    }

    if (featureIndex !== -1) {
      await AccountModel.update({ _id: account._id }, { $pull: { features: req.feature } });
    } else {
      account.features.push(req.feature);
      await account.save();
    }
  }

  res.send({ success: true });
});

router.get('/generate-template-previews', async (req, res) => {
  let names = (await TemplateModel.find({}, { _id: 0, name: 1 })).map((t) => t.name);
  for (let name of names) {
    await uploadAndSavePreviewsForTemplate({ userId: 1, name });
  }
  res.send({ success: true });
});

router.get('/generate-template-previews-name', async (req, res) => {
  let name = req.query.name;
  await uploadAndSavePreviewsForTemplate({ userId: 1, name });
  res.send({ success: true });
});

router.get('/generate-template-locales', async (req, res) => {
  const templates = await TemplateModel.find({}, { name: 1 });

  const operations = templates.map((t) => {
    const locale = /_hu/.test(t.name) ? 'hu' : 'en';
    return {
      updateOne: {
        filter: { _id: t._id },
        update: { $set: { locale } },
      },
    };
  });

  await TemplateModel.bulkWrite(operations);

  res.send({ success: true });
});

router.get('/migrate-database', [validateUserIds], async (req, res) => {
  req.log.info('start migration', req.userIds);

  try {
    await migrateAllDatas(req.userIds);
  } catch (e) {
    req.log.error(e);
    return res.status(500).send(e.message);
  }
  return res.send();
});

const getVariantTemplate = async ({ campaignId, variantId, databaseId }) => {
  return VariantTemplateModel.findOne({
    campaignId,
    databaseId,
    variantId,
  });
};

router.get('/copy-campaign', async (req, res) => {
  const { fromUser, caId, toUser } = req.query;
  if (fromUser == null || caId == null || toUser == null) {
    res.status(422).send('missing at least one param => fromUser, caId, toUser');
    return;
  }

  const [fromAccount, toAccount] = await Promise.all([
    AccountModel.findOne({ databaseId: fromUser }, { features: 1 }),
    AccountModel.findOne({ databaseId: toUser }, { settings: 1, features: 1 }),
  ]);

  const fromAccountMigrated = isFeatureEnabled(fromAccount, FEATURES.VARIANT_TEMPLATE_MIGRATED);
  const toAccountMigrated = isFeatureEnabled(toAccount, FEATURES.VARIANT_TEMPLATE_MIGRATED);
  const bothMigrated = fromAccountMigrated && toAccountMigrated;

  if (fromAccountMigrated !== toAccountMigrated) {
    res.status(400).send(`Migration status of accounts are different`);
    return;
  }

  req.log.info('copyCampaign start copy');
  const fromCampaignModel = campaignModel(fromUser);
  const toCampaignModel = campaignModel(toUser);
  let campaign;

  if (bothMigrated) {
    campaign = await fromCampaignModel.findOne({ id: caId });
  } else {
    campaign = await fromCampaignModel.findOne({ id: caId }).select('+variants.template');
  }

  if (!campaign) {
    res.status(422).send('campaign not found');
    return;
  }
  req.log.info('copyCampaign source campaignFound');
  const targetDomain = toAccount.settings.domains[0];

  const clonedCampaign = JSON.parse(JSON.stringify(campaign));
  let campaignCount = await toCampaignModel.getMaxCampaignCount();
  campaignCount += 1;
  const newCampaignId = ObjectId();
  const oldCampaignId = clonedCampaign._id;
  clonedCampaign._id = newCampaignId;
  clonedCampaign.id = campaignCount;
  if (targetDomain) {
    clonedCampaign.domainId = targetDomain._id;
    clonedCampaign.domain = targetDomain.domain;
  } else {
    clonedCampaign.domainId = null;
    clonedCampaign.domain = null;
  }
  clonedCampaign.impressions = 0;
  clonedCampaign.conversions = 0;
  clonedCampaign.conversionRate = 0;
  clonedCampaign.schedule = null;
  clonedCampaign.status = 'inactive';
  clonedCampaign.settings.integrations = [];
  const newVariantMap = {};
  const notDeletedVariants = clonedCampaign.variants.filter((v) => v.status !== 'deleted');
  let variantTemplates = [];
  for (let v of notDeletedVariants) {
    const oldVariantId = v._id;
    let newVariantId = ObjectId();
    newVariantMap[v._id] = newVariantId;
    v._id = newVariantId;
    v.id = undefined;
    if (bothMigrated) {
      const variantTemplate = await getVariantTemplate({
        campaignId: caId,
        variantId: oldVariantId,
        databaseId: fromUser,
      });
      if (variantTemplate) {
        variantTemplates.push({
          databaseId: toUser,
          campaignId: clonedCampaign.id,
          variantId: newVariantId,
          template: regenerateTemplateIds(variantTemplate.template),
        });
      }
    } else {
      v.template = regenerateTemplateIds(v.template);
    }

    v.previewURLs = [];
    v.lastActivatedDate = null;
    v.previewGeneratedAt = null;
    v.impressions = 0;
    v.conversions = 0;
  }
  clonedCampaign.createdAt = new Date();
  clonedCampaign.updatedAt = new Date();
  req.log.info('copyCampaign saving campaign');
  const savedCampaign = await toCampaignModel.create(clonedCampaign);
  if (bothMigrated) {
    req.log.info('copyCampaign copy variant templates');
    await VariantTemplateModel.insertMany(variantTemplates);
  }
  const newVariant = savedCampaign.variants[0];
  let images;

  if (bothMigrated) {
    req.log.info('copyCampaign copy variant templates');
    await VariantTemplateModel.insertMany(variantTemplates);
    images = variantTemplates[0].template.images;
  } else {
    images = newVariant.template.images;
  }

  if (images.length > 0) {
    req.log.info('copyCampaign copy images');
    const toStr = `userImages/${toUser}/${newVariant._id}`;

    const imageReplaces = [];
    for (let image of images) {
      // Copy image in S3
      const { _id: sourceId, url: sourceUrl } = image;
      const match = sourceUrl.match(/.*\/userImages\/(\w+)\/(\w+)\/(.*)/);
      const sourceVariant = match[2];
      const fromStr = `userImages/${fromUser}/${sourceVariant}`;
      const fromIndex = sourceUrl.indexOf(fromStr);
      const fromUrl = sourceUrl.substr(0, fromIndex);
      const from = sourceUrl.substr(fromIndex);
      const to = from.replace(fromStr, toStr);
      req.log.info(`copyCampaign copy image ${from} -> ${to}`);
      // Insert image into DB
      const fromImage = await imageModel.findOne({
        _id: sourceId,
        databaseId: fromUser,
      });
      if (fromImage) {
        const clonedImage = JSON.parse(JSON.stringify(fromImage));
        const targetImageId = ObjectId();
        clonedImage._id = targetImageId;
        clonedImage.databaseId = toUser;
        clonedImage.sourceImage = null;
        clonedImage.url = fromUrl + to;
        clonedImage.createdAt = new Date();
        clonedImage.updatedAt = new Date();
        req.log.info(`copyCampaign insertImage`);
        await imageModel.create(clonedImage);
        await S3.copyInBucket({ from, to });
        imageReplaces.push({
          fromUrl: sourceUrl,
          toUrl: fromUrl + to,
          fromId: sourceId.toString(),
          toId: targetImageId.toString(),
        });
      }
    }

    let templateString;

    if (bothMigrated) {
      templateString = JSON.stringify(variantTemplates[0].template);
    } else {
      templateString = JSON.stringify(newVariant.template);
    }

    imageReplaces.forEach((e) => {
      templateString = templateString.replace(new RegExp(e.fromUrl, 'g'), e.toUrl);
      templateString = templateString.replace(new RegExp(e.fromId, 'g'), e.toId);
    });

    if (bothMigrated) {
      await VariantTemplateModel.updateOne(
        { databaseId: toUser, campaignId: savedCampaign.id, variantId: newVariant._id },
        { $set: { 'variants.0.template': JSON.parse(templateString) } },
      );
    } else {
      await toCampaignModel.updateOne(
        { _id: savedCampaign._id },
        { $set: { 'variants.0.template': JSON.parse(templateString) } },
      );
    }

    updateAccountCampaignInfo(toUser);
  }

  let subscribers = await SubscriberModel.find({
    campaignId: oldCampaignId,
    databaseId: fromUser,
  });
  subscribers.forEach((e) => {
    e.campaignId = newCampaignId;
    e.databaseId = toUser;
    e.variantId = newVariantMap[e.variantId];
  });
  if (subscribers.length > 0) {
    req.log.info(`copyCampaign copy subscribers`, subscribers.length);
    await SubscriberModel.insertMany(subscribers);
  }

  let stats = await StatsModel.find({ databaseId: fromUser, campaign: oldCampaignId });
  stats.forEach((e) => {
    e.campaign = newCampaignId;
    e.variant = newVariantMap[e.variant];
    e.databaseId = toUser;
  });
  if (stats.length > 0) {
    req.log.info(`copyCampaign copy stats`, stats.length);
    await StatsModel.insertMany(stats);
  }

  res.send({ success: true });
});

router.get('/set-gamification-templates', async (req, res) => {
  const templates = await TemplateModel.find({});

  const toSet = [];

  for (const template of templates) {
    const templateString = JSON.stringify(template.template);

    if (isGamificationTemplate(templateString)) {
      toSet.push(template._id);
    }
  }

  await TemplateModel.updateMany({ _id: { $in: toSet } }, { $set: { gamification: true } });

  res.send({ success: true });
});

router.get('/set-survey-templates', async (req, res) => {
  const templates = await TemplateModel.find({});

  const toSet = [];

  for (const template of templates) {
    const templateString = JSON.stringify(template.template);

    if (isSurveyTemplate(templateString)) {
      toSet.push(template._id);
    }
  }

  console.log('@@@toSet.length', toSet.length);

  await TemplateModel.updateMany({ _id: { $in: toSet } }, { $set: { survey: true } });

  res.send({ success: true });
});

router.get('/inactive-midmarket-ping', async (req, res) => {
  const midmarketAccounts = await AccountModel.find(
    {
      'billing.package': {
        $in: midmarketPackages,
      },
    },
    {
      databaseId: 1,
      'settings.messages.midmarketNoActiveCampaignPing': 1,
      users: 1,
    },
  );

  const firstPhase = [];
  const secondPhase = [];

  for (const midmarketAccount of midmarketAccounts) {
    const activeCampaignCount = await campaignModel(midmarketAccount.databaseId).countDocuments({
      status: 'active',
    });

    if (activeCampaignCount === 0) {
      if (midmarketAccount.settings.messages.midmarketNoActiveCampaignPing) {
        secondPhase.push(midmarketAccount.databaseId);

        const csm = await BackofficeCsm.findOne({
          databaseId: midmarketAccount.databaseId,
        });

        if (csm && csm.owner) {
          const user = midmarketAccount.users.find((u) => u.role === 'owner');
          const login = await LoginModel.findOne({ _id: user.loginId });

          const owner = await salesMysql.getUserById(csm.owner);

          if (owner.email) {
            const mailConfig = {
              from: Mailer.getFromByRegion(REGIONS.USA),
              to: `<${owner.email}>`,
              subject: `Inactive midmarket user`,
              text: `The following customer (${login.firstName} ${login.lastName} - ${login.email}) turned into an inactive user.`,
              html: `The following customer (${login.firstName} ${login.lastName} - ${login.email}) turned into an inactive user.`,
            };

            await Mailer.sendMail(mailConfig);
          }
        }
      } else {
        firstPhase.push(midmarketAccount.databaseId);
      }
    }
  }

  await Promise.all([
    AccountModel.updateMany(
      { databaseId: { $in: firstPhase } },
      { $set: { 'settings.messages.midmarketNoActiveCampaignPing': 1 } },
    ),
    AccountModel.updateMany(
      { databaseId: { $in: secondPhase } },
      { $set: { 'settings.messages.midmarketNoActiveCampaignPing': null } },
    ),
  ]);

  res.send({ success: true });
});

router.get('/jf-cred-sync', async (req, res) => {
  if (!isJFCredentialSyncBusy) {
    try {
      isJFCredentialSyncBusy = true;
      const queryFilter = {
        'billing.dateExpires': { $gt: moment().subtract(15, 'days').toDate() },
        'settings.shops.type': { $in: ['shopify', 'shoprenter'] },
        // $or: [
        //   { 'settings.shops.myshopify_domain': { $in: ['omstore18.myshopify.com', 'kissmyketo.myshopify.com', 'reflexshop', 'whiskynet', 'vagyaim2', 'sexshopcenter', 'btsiker', 'biozoo', 'naturali', 'summer', 'killcliff.myshopify.com', 'killcliffcbd.myshopify.com'] } },
        //   { 'settings.shops.shopname': { $in: ['omstore18.myshopify.com', 'kissmyketo.myshopify.com', 'reflexshop', 'whiskynet', 'vagyaim2', 'sexshopcenter', 'btsiker', 'biozoo', 'naturali', 'summer'] } }
        // ],
        'settings.shops.active': { $in: [1, true, '1'] },
      };
      const count = await AccountModel.countDocuments(queryFilter);
      const BATCH_SIZE = 10;
      let synced = 0;

      req.log.info('Going to sync shop credentials of %d shops.', count);

      while (synced < count) {
        const accounts = await AccountModel.find(queryFilter, {
          _id: 1,
          'settings.shops': 1,
          databaseId: 1,
        })
          .skip(synced)
          .limit(BATCH_SIZE);

        for (const account of accounts) {
          const {
            databaseId,
            settings: { shops },
          } = account;

          await Promise.all(
            shops.map(async (shop, idx) => {
              const {
                active,
                type,
                shopname,
                username,
                password,
                myshopify_domain: myshopifyDomain,
                oauth_token: accessToken,
                scopes,
              } = shop;
              if (![1, '1', true].includes(active) || !['shoprenter', 'shopify'].includes(type)) {
                return false;
              }

              let shopId = null;
              let creds;
              if (type === 'shoprenter') {
                shopId = shopname;
                creds = {
                  username,
                  password,
                };
              } else {
                shopId = myshopifyDomain;
                creds = {
                  accessToken,
                  scopes,
                };
              }

              await handleShopInstall(databaseId, shopId, type, creds);

              return true;
            }),
          );
        }

        synced += accounts.length;
        req.log.info(
          '%d/%d (%s) of shop credentials done.',
          synced,
          count,
          ((synced / count) * 100).toFixed(2),
        );
      }
      req.log.info('Finished shop credential syncing');

      res.status(200).end();
    } catch (err) {
      req.log.error(err, 'Error while updating shop credentials in jetfabric backend');
      return res.status(500).json({ success: false }).end();
    } finally {
      isJFCredentialSyncBusy = false;
    }
  } else {
    return res.status(422).send({ busy: true }).end();
  }
});

router.get('/set-affiliate-partner-info', async (req, res) => {
  try {
    const affiliates = await AccountModel.find({
      'settings.general.key': 'affiliate',
      'settings.general.value': { $in: ['1', 'true', true] },
    });

    const loginIds = affiliates.map((a) => a.users.find((uu) => uu.role === 'owner').loginId);
    const logins = await LoginModel.find({ _id: { $in: loginIds } });

    const operations = [];
    const slugs = [];

    for (const account of affiliates) {
      const ownerLogin = account.users.find((uu) => uu.role === 'owner');
      const login = logins.find((l) => l._id.toString() === ownerLogin.loginId.toString());

      const displayName = `${login.firstName} ${login.lastName}`.trim();

      let slug = slugify(displayName);
      if (slugs.includes(slug)) {
        let repetation = 1;
        let postfix = '';
        while (slugs.includes(`${slug}${postfix}`)) {
          postfix = `-${repetation}`;
          repetation++;
        }
        slug = `${slug}${postfix}`;
      }

      slugs.push(slug);
      operations.push({
        updateOne: {
          filter: { databaseId: account.databaseId },
          update: {
            $set: {
              'settings.affiliate.partnerInfo.displayName': displayName,
              'settings.affiliate.partnerInfo.slug': slug,
            },
          },
        },
      });
    }

    if (operations.length) {
      await AccountModel.bulkWrite(operations);
    }

    res.sendStatus(200);
  } catch (e) {
    console.log(e);
    res.sendStatus(500);
  }
});

// fixes affiliate setting in mongo for agencies
router.get('/migrate-agency-affiliate', async (req, res) => {
  try {
    const dontHaveAffiliateSetting = await AccountModel.find(
      {
        type: 'agency',
        'settings.general.key': 'affiliate',
        'settings.general.value': { $nin: ['1', 'true', true] },
      },
      { databaseId: 1 },
    );
    const shouldBeAffiliate = await salesMysql.getAffiliatesByDatabaseIds(
      dontHaveAffiliateSetting.map((a) => a.databaseId),
    );
    await AccountModel.updateMany(
      {
        databaseId: { $in: shouldBeAffiliate },
        'settings.general.key': 'affiliate',
      },
      { 'settings.general.$.value': true },
    );

    let haveAffiliateSetting = await AccountModel.find(
      {
        type: 'agency',
        'settings.general.key': 'affiliate',
        'settings.general.value': { $in: ['1', 'true', true] },
      },
      { databaseId: 1 },
    );
    haveAffiliateSetting = haveAffiliateSetting.map((a) => a.databaseId);
    const affiliates = await salesMysql.getAffiliatesByDatabaseIds(haveAffiliateSetting);
    const shouldNotBeAffiliate = _difference(haveAffiliateSetting, affiliates);

    await AccountModel.updateMany(
      {
        databaseId: { $in: shouldNotBeAffiliate },
        'settings.general.key': 'affiliate',
      },
      { 'settings.general.$.value': false },
    );

    req.log.info(`successfully migrated affiliate users`);

    res.send({
      shouldBeAffiliate,
      shouldNotBeAffiliate,
    });
  } catch (e) {
    req.log.error(`error while migrating affiliate users, ${e}`);
    res.sendStatus(500);
  }
});

router.get('/set-multicolor-siblings', async (req, res) => {
  try {
    await templateService.setMulticolorTemplateSiblings();
    res.send({ success: true });
  } catch (e) {
    res.send(e.message);
  }
});

router.get('/set-wizard-use-cases-config', async (req, res) => {
  try {
    await useCaseService.setWizardUseCasesConfig();
    res.send({ success: true });
  } catch (e) {
    res.send(e.message);
  }
});

router.get('/set-universal-preview-for-use-cases', async (req, res) => {
  try {
    await useCaseService.setUniversalPreviewForUseCases();
    res.send({ success: true });
  } catch (e) {
    res.send(e.message);
  }
});

router.get('/set-goals-for-universal-templates', async (req, res) => {
  try {
    await useCaseService.setUniversalTemplateGoalsByUseCases();
    res.send({ success: true });
  } catch (e) {
    res.send(e.message);
  }
});

router.get('/set-goals-for-use-cases', async (req, res) => {
  try {
    await useCaseService.setGoalsForUseCases();
    res.send({ success: true });
  } catch (e) {
    res.send(e.message);
  }
});

router.get('/download-templates', async (req, res) => {
  try {
    await downloadTemplates();
  } catch (err) {
    return res.status(err.status).send({
      success: false,
      message: err.message,
    });
  }
  res.send({ success: true });
});

router.get('/upload-templates', async (req, res) => {
  try {
    req.log.info({ message: 'Starting template upload' });
    await uploadTemplates();
  } catch (err) {
    req.log.error({ message: 'Error while uploading templates', err });
    return res.status(err.status).send({
      success: false,
      message: err.message,
    });
  }

  res.send({ success: true });
});

router.get('/update-service-integrations', async (req, res) => {
  try {
    await updateServiceIntegrationsMaterializedView();
  } catch (err) {
    req.log.error(err, 'Error while running update service integrations materialised view');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/translate-templates-reset-cache', async (req, res) => {
  try {
    await delKeysWithScan('translations_*');
  } catch (e) {
    req.log.error({ message: 'Error during delete translation redis', error: e });
    return res.send('KO');
  }

  return res.send('OK');
});

router.get('/translate-templates', async (req, res) => {
  let templateCount = 0;
  let translatedCount = 0;
  let success = true;
  let errorMessage = '';
  const { theme, templateName, lang } = req.query;
  if (!theme && !templateName) return res.status(400).send('Missing parameter');

  try {
    const query = theme ? { theme: new RegExp(`^${theme}$`, 'i') } : { name: templateName };
    const templates = await TemplateModel.find({ ...query, status: 'published', locale: 'en' });
    templateCount = templates.length;
    req.log.info(`Translating ${templateCount} templates`);
    for (const [index, template] of templates.entries()) {
      try {
        req.log.info({
          message: 'Starting template translate',
          index,
          templateCount,
          theme,
          templateName,
        });
        if (await TemplateTranslator.translate(template, lang)) {
          translatedCount++;
        }
      } catch (e) {
        req.log.error(e, `Error while translating template with id: ${template._id}`);
      }
    }
  } catch (e) {
    req.log.error(e, 'Error while translating templates');
    success = false;
    errorMessage = e.message;
  }

  res.send({ success, templateCount, translatedCount, errorMessage });
});

router.get('/update-template-fuzzy-search', async (req, res) => {
  if (!isFuzzySearchBusy) {
    isFuzzySearchBusy = true;
    const result = await updateTemplateFuzzySearch();
    isFuzzySearchBusy = false;
    return res.send(result);
  }

  return res.status(422).send({ busy: true });
});

router.get('/sppo-auto-generate', async (req, res) => {
  try {
    const params = req.query;
    const promptId = params?.promptId ?? null;
    const databaseId = params?.databaseId ?? null;
    const useDateFilter = params?.dateFilter !== 'false';
    const loggerService = new SPPOAutoGenerateLogger({
      logger: req.log,
      type: 'SPPO-auto-generate',
    });
    const service = new SPPOAutoGenerate({ loggerService, promptId, databaseId, useDateFilter });
    await service.runGenerate();
  } catch (err) {
    req.log.error(err, 'Error while running SPPO auto generate');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/sppo-auto-publish', async (req, res) => {
  try {
    const params = req.query;
    const databaseId = params?.databaseId ?? null;
    const loggerService = new SPPOAutoGenerateLogger({
      logger: req.log,
      type: 'SPPO-auto-publish',
    });
    const service = new SPPOAutoPublish({ loggerService, databaseId });
    await service.runPublish();
  } catch (err) {
    req.log.error(err, 'Error while running SPPO auto publish');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/spr-embedding', async (req, res) => {
  try {
    const params = req.query;
    const databaseId = params?.databaseId ?? null;

    const SPRlist = await getSPRList(databaseId);
    for (const SPR of SPRlist) {
      await triggerEmbedding(SPR, req.log);
    }
  } catch (err) {
    req.log.error(err, 'Error while running SPR embedding');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/product-feed-sync', async (req, res) => {
  try {
    const params = req.query;
    const databaseId = params?.databaseId ?? null;

    const productFeedSyncList = await getProductFeedSyncList(databaseId);
    for (const feed of productFeedSyncList) {
      await triggerProductSyncFlow(feed, req.log);
    }
  } catch (err) {
    req.log.error(err, 'Error while running Product feed sync');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/detect-nonstandard-orders', async (req, res) => {
  try {
    req.log.info('Start detect nonstandard orders');
    await detectNonstandardOrderShops();
    req.log.info('Finished detect nonstandard orders');
  } catch (err) {
    req.log.error(err, 'Detecting nonstandard orders failed');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

const evaluateSAB = async () => {
  const changeGroups = await getChangesForEvaluation();

  if (!changeGroups?.length) {
    return;
  }

  await SmartABTestEvaluator.refreshBQViews(changeGroups);

  for (const group of changeGroups) {
    const metrics = await SmartABTestEvaluator.queryMetrics(group);
    for (const changeData of group.changes) {
      const evaluator = new SmartABTestEvaluator(changeData);
      await evaluator.run(metrics);
    }
  }
};

router.get('/evaluate-smart-ab-test', async (_, res) => {
  evaluateSAB();

  res.send({ success: true });
});

router.get('/user-alert-emails', async (req, res) => {
  const logger = req.log;
  const params = req.query;
  const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;
  const dryRun = params?.dryRun === 'true';

  const LOCK_KEY = 'cron-lock:send-user-alert-emails';

  if (!filteredDatabaseId) {
    const lock = await redis.get(LOCK_KEY);
    if (lock) {
      return res.send({ locked: lock });
    }

    await redis.setex(LOCK_KEY, new Date().toISOString(), 600); // 10 minute
  }

  // eslint-disable-next-line no-unneeded-ternary
  const runInBackground = params?.runInBg === 'false' ? false : true;

  if (runInBackground) {
    res.send({ start: true });
  }

  const userAlertList = await userAlertEmailScheduler.getUserAlertList(
    { filteredDatabaseId },
    logger,
  );
  const sendResult = await userAlertEmailScheduler.sendUserAlertEmails(
    userAlertList,
    logger,
    dryRun,
  );

  if (!runInBackground) {
    res.send({ summary: sendResult.summary });
  }

  await redis.del(LOCK_KEY);
});

router.get('/weekly-campaign-report-emails', async (req, res) => {
  const params = req.query;
  const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;

  const dryRun = params?.dryRun === 'true';
  const sendTo = params?.sendTo;

  const LOCK_KEY = 'cron-lock:weekly-campaign-report-emails';

  if (!filteredDatabaseId) {
    const lock = await redis.get(LOCK_KEY);
    if (lock) {
      return res.send({ locked: lock });
    }

    await redis.setex(LOCK_KEY, new Date().toISOString(), 600); // 10 minute
  }

  const interval = {
    from: moment().subtract(1, 'weeks').startOf('isoWeek').toDate(), // Previous Monday
    to: moment().subtract(1, 'weeks').endOf('isoWeek').toDate(), // Previous Sunday
  };

  const accountCursor = await getMailList(filteredDatabaseId, interval);

  for await (const account of accountCursor) {
    await queueCampaignReport({ account, dryRun, sendTo, interval });
  }

  await redis.del(LOCK_KEY);

  res.send({ start: true });
});

// Frequency rule V2
router.get('/frequency-rule-v2-migration', async (req, res) => {
  const logger = req.log;
  const params = req.query;
  try {
    const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;

    if (!filteredDatabaseId) {
      res.send({ start: true });
    }

    const result = await frequencyRuleMigrator.run(
      filteredDatabaseId ? [filteredDatabaseId] : null,
    );
    if (filteredDatabaseId) {
      res.send({ success: true, ...result });
    }
  } catch (err) {
    logger.error(
      { err, databaseId: params?.databaseId },
      'Error while running visitor cart v3 rule migration',
    );
  }
});

// Visitor Cart V3
router.get('/visitor-cart-v3-migration', async (req, res) => {
  const logger = req.log;
  const params = req.query;
  try {
    const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;

    if (!filteredDatabaseId) {
      res.send({ start: true });
    }

    const result = await visitorCartMigrator.run(filteredDatabaseId ? [filteredDatabaseId] : null);
    if (filteredDatabaseId) {
      res.send({ success: true, ...result });
    }
  } catch (err) {
    logger.error(
      { err, databaseId: params?.databaseId },
      'Error while running visitor cart v3 rule migration',
    );
  }
});

router.get('/visitor-cart-v3-migration-revert', async (req, res) => {
  const logger = req.log;

  try {
    res.send({ start: true });

    await visitorCartMigrator.revert();
  } catch (err) {
    logger.error({ err }, 'Error while revert visitor cart v3 rule migration');
  }
});

router.get('/variant-template-migration', async (req, res) => {
  const logger = req.log;
  const params = req.query;
  try {
    const accountId = params?.databaseId ? parseInt(params.databaseId, 10) : null;
    const shouldCleanUpTemplate = params?.cleanupTemplate
      ? params.cleanupTemplate === 'true'
      : false;

    if (!accountId) {
      return res.send({ success: false, error: 'Missing databaseId' });
    }

    await runVariantTemplateMigration({ accountId, shouldCleanUpTemplate });

    res.send({ success: true });
  } catch (err) {
    res.send({ success: false });
    logger.error(
      { err, databaseId: params?.databaseId },
      'Error while running variant template migration',
    );
  }
});

router.get('/revert-variant-template-migration', async (req, res) => {
  const logger = req.log;
  const params = req.query;
  try {
    const accountId = params?.databaseId ? parseInt(params.databaseId, 10) : null;

    if (!accountId) {
      return res.send({ success: false, error: 'Missing databaseId' });
    }

    await revertVariantTemplateMigration({ accountId });

    res.send({ success: true });
  } catch (err) {
    res.send({ success: false });
    logger.error(
      { err, databaseId: params?.databaseId },
      'Error while reverting variant template migration',
    );
  }
});

// 2024 Current URL rule revamp
router.get('/current-url-rule-migration', async (req, res) => {
  const logger = req.log;

  try {
    const params = req.query;
    const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;
    const limit = params?.limit ? parseInt(params.limit, 10) : 1;
    const skip = params?.skip ? parseInt(params.skip, 10) : 0;
    const delayInMs = params?.delayInMs ? parseInt(params.delayInMs, 10) : 500;

    const accountList = await currentUrlRuleMigrator.getAccountList({
      filteredDatabaseId,
      limit,
      skip,
    });
    const immediateResponse = !filteredDatabaseId && accountList.length > 10;

    const responseData = {
      limit,
      skip,
      delayInMs,
      nofAccounts: accountList.length,
      accountIds: accountList.map((account) => account.databaseId),
    };

    if (immediateResponse) {
      res.send({ start: true, ...responseData });
    }

    const summary = await currentUrlRuleMigrator.runMigration(logger, accountList, delayInMs);

    if (!immediateResponse) {
      res.send({ success: true, ...responseData, summary });
    }
  } catch (err) {
    logger.error(err, 'Error while running Current URL rule migration');
    return res.status(500).send({
      success: false,
      message: err?.message ?? null,
    });
  }
});

// 2024 Current URL rule revamp - Global segment migration
router.get('/current-url-rule-segment-migration', async (req, res) => {
  const logger = req.log;

  try {
    const segmentList = await currentUrlRuleGlobalSegmentMigrator.getSegmentList();
    await currentUrlRuleGlobalSegmentMigrator.runMigration(logger, segmentList);
    res.send({ success: true, nofSegments: segmentList.length });
  } catch (err) {
    logger.error(err, 'Error while running Current URL rule global segment migration');
    return res.status(500).send({
      success: false,
      message: err?.message ?? null,
    });
  }
});

// 2024 Current URL rule revamp - revert migration
router.get('/current-url-rule-revert-migration', async (req, res) => {
  const logger = req.log;

  try {
    const params = req.query;
    const filteredDatabaseId = params?.databaseId ? parseInt(params.databaseId, 10) : null;

    await currentUrlRuleMigrator.runRevertMigration({
      logger,
      filteredDatabaseId,
    });
    res.send({ success: true });
  } catch (err) {
    logger.error(err, 'Error while running Current URL rule revert migration');
    return res.status(500).send({
      success: false,
      message: err?.message ?? null,
    });
  }
});

router.get('/delete-removed-service-integrations', async (req, res) => {
  try {
    req.log.info('Start deleting removed service integrations');
    await deleteRemovedShops();
    req.log.info('Finished deleting removed service integrations');
  } catch (err) {
    req.log.error(err, 'Deleting removed service integrations failed');
    return res.status(500).send({
      success: false,
    });
  }

  res.send({ success: true });
});

router.get('/generate-site-data', async (req, res) => {
  const dds = await getDDSConnectionHandler();
  const ddsResult = await dds.aggregate(process.env.dds_collection_name, [
    { $unwind: '$domains' },
    { $match: { 'statistics.lastAppearanceAt': { $gt: new Date('2025-05-04') } } },
    { $project: { databaseId: 1, domains: 1 } },
  ]);

  for (let [i, account] of ddsResult.entries()) {
    const { databaseId, domains: domain } = account;
    try {
      autoThemeGenerator.Query.generateAutoTheme(
        null,
        { domainId: domain._id.toString() },
        { userId: databaseId, log: req.log, req },
      );

      const result = await siteDataGenerator.Query.generateSiteExtractions(
        null,
        { domainId: domain._id.toString() },
        { userId: databaseId, log: req.log },
      );

      if (result) {
        req.log.info(`Generated site data for ${databaseId} user ${domain.domain} domain`);
      } else {
        req.log.error(
          `Error during generating site data for ${databaseId} user ${domain.domain} domain`,
        );
      }
    } catch (e) {
      req.log.error(
        `Error during generating site data for ${databaseId} user ${domain.domain} domain`,
        e.message,
      );
    }

    req.log.info(`Site data generator: ${++i} / ${ddsResult.length} done`);
  }
  res.send({ success: true });
});

module.exports = router;
