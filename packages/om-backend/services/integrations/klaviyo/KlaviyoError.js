/**
 * Custom error class for Klaviyo integration errors
 * This provides a consistent way to handle and format errors across the Klaviyo integration
 */
class KlaviyoError extends Error {
  /**
   * Create a new KlaviyoError
   * @param {string} message - Error message
   * @param {Object} options - Additional error options
   * @param {Error} [options.originalError] - The original error that was caught
   * @param {Object} [options.response] - The response object from the API call
   * @param {string} [options.context] - Additional context about where the error occurred
   * @param {string} [options.code] - Error code
   */
  constructor(message, options = {}) {
    super(message);
    this.name = 'KlaviyoError';

    // Store original error if provided
    if (options.originalError) {
      this.originalError = options.originalError;
      this.stack = options.originalError.stack;
    }

    // Store response data if available
    if (options.response) {
      this.status = options.response.status;
      this.responseData = options.response.data;
    }

    // Store additional context
    this.context = options.context || 'klaviyo-integration';
    this.code = options.code || 'KLAVIYO_ERROR';
  }

  /**
   * Format the error for logging
   * @returns {Object} Formatted error object
   */
  toLogFormat() {
    const logObject = {
      name: this.name,
      message: this.message,
      context: this.context,
      code: this.code,
    };

    if (this.status) {
      logObject.status = this.status;
    }

    if (this.responseData) {
      logObject.responseData = this.responseData;
    }

    if (this.originalError) {
      logObject.originalError = {
        name: this.originalError.name,
        message: this.originalError.message,
      };
    }

    return logObject;
  }

  /**
   * Create a KlaviyoError from an axios error
   * @param {Error} error - The axios error
   * @param {string} context - Context where the error occurred
   * @returns {KlaviyoError} A new KlaviyoError instance
   */
  static fromAxiosError(error, context) {
    const response = error.response || {};
    const responseData = response.data || {};
    const message = responseData.message || error.message || 'Unknown Klaviyo API error';

    return new KlaviyoError(message, {
      originalError: error,
      response: response,
      context: context,
      code: `KLAVIYO_${response.status || 'REQUEST'}_ERROR`,
    });
  }

  /**
   * Create a KlaviyoError for authentication failures
   * @param {string} message - Error message
   * @param {Error} [originalError] - Original error if available
   * @returns {KlaviyoError} A new KlaviyoError instance
   */
  static authError(message, originalError = null) {
    return new KlaviyoError(message, {
      originalError,
      context: 'klaviyo-authentication',
      code: 'KLAVIYO_AUTH_ERROR',
    });
  }

  /**
   * Create a KlaviyoError for token refresh failures
   * @param {string} message - Error message
   * @param {Error} [originalError] - Original error if available
   * @returns {KlaviyoError} A new KlaviyoError instance
   */
  static tokenRefreshError(message, originalError = null) {
    return new KlaviyoError(message, {
      originalError,
      context: 'klaviyo-token-refresh',
      code: 'KLAVIYO_TOKEN_REFRESH_ERROR',
    });
  }
}

module.exports = KlaviyoError;
