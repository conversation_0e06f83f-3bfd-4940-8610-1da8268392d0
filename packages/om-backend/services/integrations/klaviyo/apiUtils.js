/**
 * Utility functions for making Klaviyo API requests with consistent error handling
 */
const KlaviyoError = require('./KlaviyoError');

/**
 * Execute an API request with consistent error handling
 * @param {Function} requestFn - Function that returns a promise for the API request
 * @param {Object} options - Options for error handling
 * @param {string} options.context - Context for error logging
 * @param {Function} options.logger - Logger function
 * @param {boolean} [options.returnFalseOnError=false] - Whether to return false on error instead of throwing
 * @returns {Promise<*>} - The API response or false if returnFalseOnError is true and an error occurs
 * @throws {KlaviyoError} - If an error occurs and returnFalseOnError is false
 */
async function executeApiRequest(requestFn, { context, logger, returnFalseOnError = false }) {
  try {
    return await requestFn();
  } catch (error) {
    const klaviyoError = KlaviyoError.fromAxiosError(error, context);

    // Log the error
    logger.error(
      klaviyoError.toLogFormat(),
      `Error in Klaviyo API request: ${klaviyoError.message}`,
    );

    // Either return false or throw the error
    if (returnFalseOnError) {
      return false;
    }

    throw klaviyoError;
  }
}

/**
 * Safely parse API response data
 * @param {Object} response - The API response
 * @param {Function} formatter - Function to format the response data
 * @param {Object} options - Options for error handling
 * @param {string} options.context - Context for error logging
 * @param {Function} options.logger - Logger function
 * @returns {*} - The formatted response data
 * @throws {KlaviyoError} - If an error occurs during parsing
 */
function safeParseResponse(response, formatter, { context, logger }) {
  try {
    return formatter(response.data);
  } catch (error) {
    const klaviyoError = new KlaviyoError(
      `Failed to parse Klaviyo API response: ${error.message}`,
      {
        originalError: error,
        context,
        code: 'KLAVIYO_PARSE_ERROR',
      },
    );

    logger.error(klaviyoError.toLogFormat(), 'Error parsing Klaviyo API response');
    throw klaviyoError;
  }
}

/**
 * Handle token refresh with consistent error handling
 * @param {Function} refreshFn - Function that performs the token refresh
 * @param {Object} options - Options for error handling
 * @param {string} options.context - Context for error logging
 * @param {Function} options.logger - Logger function
 * @param {boolean} [options.returnFalseOnError=false] - Whether to return false on error instead of throwing
 * @returns {Promise<boolean>} - True if refresh was successful, false if returnFalseOnError is true and an error occurs
 * @throws {KlaviyoError} - If an error occurs and returnFalseOnError is false
 */
async function handleTokenRefresh(refreshFn, { context, logger, returnFalseOnError = false }) {
  try {
    return await refreshFn();
  } catch (error) {
    const klaviyoError = KlaviyoError.tokenRefreshError(
      `Failed to refresh Klaviyo token: ${error.message}`,
      error,
    );

    logger.error(klaviyoError.toLogFormat(), 'Error refreshing Klaviyo token');

    if (returnFalseOnError) {
      return false;
    }

    throw klaviyoError;
  }
}

module.exports = {
  executeApiRequest,
  safeParseResponse,
  handleTokenRefresh,
};
