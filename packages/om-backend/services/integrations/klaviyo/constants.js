/**
 * Shared constants for Klaviyo integration
 * This file centralizes all Klaviyo-related constants to avoid duplication
 * and ensure consistency across the codebase.
 */

// API configuration
const API_VERSION = process.env.KLAVIYO_API_VERSION || '2025-04-15';
const BASE_URL = 'https://a.klaviyo.com';
const API_BASE_URL = `${BASE_URL}/api`;
const TOKEN_ENDPOINT = `${BASE_URL}/oauth/token`;
const AUTH_ENDPOINT = 'https://www.klaviyo.com/oauth/authorize';

// OAuth credentials
// Note: In production, these should be stored in environment variables
const CLIENT_ID = process.env.KLAVIYO_CLIENT_ID || 'fc5b62d5-e7f8-47a9-8803-b114a4dbfd16';
const CLIENT_SECRET =
  process.env.KLAVIYO_CLIENT_SECRET ||
  'wXmjS2loK5GMj-uLUm4zD9vz6Dd-SsL10Ovlxxi1b-bM3BV7uVAe9lPTKSLQjBZlBY-ZbP9skAo_IWdHYWpghg';

// OAuth scopes
const DEFAULT_SCOPES = [
  'subscriptions:write',
  'profiles:read',
  'profiles:write',
  'lists:read',
  'lists:write',
];
const DEFAULT_SCOPES_STRING = DEFAULT_SCOPES.join(' ');

// Redis key patterns
const REDIS_CODE = (userId) => `klaviyoOAuthCode-${userId}`;
const REDIS_STATE = (userId) => `klaviyoOAuthState-${userId}`;

// Helper functions
const getBasicAuthorizationHeader = () => {
  return `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`;
};

module.exports = {
  API_VERSION,
  BASE_URL,
  API_BASE_URL,
  TOKEN_ENDPOINT,
  AUTH_ENDPOINT,
  CLIENT_ID,
  CLIENT_SECRET,
  DEFAULT_SCOPES,
  DEFAULT_SCOPES_STRING,
  REDIS_CODE,
  REDIS_STATE,
  getBasicAuthorizationHeader,
};
