const axios = require('axios');
const log = require('../../logger').child({ integration: 'klaviyo' });
const { API_VERSION, API_BASE_URL } = require('./klaviyo/constants');
const KlaviyoError = require('./klaviyo/KlaviyoError');
const { executeApiRequest, safeParseResponse } = require('./klaviyo/apiUtils');

class KlaviyoAdapter {
  constructor({ apiKey }) {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        Authorization: `Klaviyo-API-Key ${apiKey}`,
        revision: API_VERSION,
      },
    });

    this.lists = [];
    this.segments = [];
  }

  async ping() {
    return executeApiRequest(
      async () => {
        const response = await this.api.get('/lists');
        this.lists = safeParseResponse(response, this._formatList.bind(this), {
          context: 'klaviyo-ping-parse',
          logger: log,
        });
        return true;
      },
      {
        context: 'klaviyo-ping',
        logger: log,
        returnFalseOnError: true,
      }
    );
  }

  async getData() {
    try {
      await this.getLists();

      return { success: true, lists: this.lists };
    } catch (error) {
      log.error(
        error instanceof KlaviyoError ? error.toLogFormat() : error,
        'Error getting data from Klaviyo'
      );
      return {
        success: false,
        error: error instanceof KlaviyoError ? error.message : error.response?.data?.message || error.message || 'Unknown error'
      };
    }
  }

  async getSegmentsAndList() {
    return executeApiRequest(
      async () => {
        await Promise.all([this.getSegments(), this.getLists()]);
        return [...this.lists, ...this.segments];
      },
      {
        context: 'klaviyo-get-segments-and-lists',
        logger: log,
      }
    );
  }

  async getSegments() {
    return executeApiRequest(
      async () => {
        this.segments = [];
        let nextPage;
        do {
          const response = await this.api.get(nextPage ?? '/segments');
          const formattedData = safeParseResponse(response, this._formatList.bind(this), {
            context: 'klaviyo-get-segments-parse',
            logger: log,
          });

          this.segments.push(...formattedData);
          nextPage = response.data.links.next;
        } while (nextPage);

        return this.segments;
      },
      {
        context: 'klaviyo-get-segments',
        logger: log,
      }
    );
  }

  async getLists() {
    return executeApiRequest(
      async () => {
        this.lists = [];
        let nextPage;
        do {
          const response = await this.api.get(nextPage ?? '/lists?sort=name');
          const formattedData = safeParseResponse(response, this._formatList.bind(this), {
            context: 'klaviyo-get-lists-parse',
            logger: log,
          });

          this.lists.push(...formattedData);
          nextPage = response.data.links.next;
        } while (nextPage);

        return this.lists;
      },
      {
        context: 'klaviyo-get-lists',
        logger: log,
      }
    );
  }

  _formatList({ data }) {
    return data.map((item) => {
      const { id, attributes, type: listType } = item;
      return {
        id,
        name: attributes.name,
        listType,
      };
    });
  }
}

module.exports = KlaviyoAdapter;
