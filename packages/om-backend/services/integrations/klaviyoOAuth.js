const querystring = require('querystring');
const axios = require('axios');
const log = require('../../logger').child({ integration: 'klaviyo-oauth' });
const { refreshTokenForAuth } = require('../../util/oauth');
const {
  API_VERSION,
  API_BASE_URL,
  TOKEN_ENDPOINT,
  AUTH_ENDPOINT,
  CLIENT_ID,
  DEFAULT_SCOPES,
  getBasicAuthorizationHeader,
} = require('./klaviyo/constants');
const KlaviyoError = require('./klaviyo/KlaviyoError');
const { executeApiRequest, safeParseResponse, handleTokenRefresh } = require('./klaviyo/apiUtils');

class KlaviyoOAuthAdapter {
  constructor({ access_token, refresh_token, expires_at }) {
    this.accessToken = access_token;
    this.refreshToken = refresh_token;
    this.expiresAt = new Date(expires_at);

    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        Accept: 'application/json',
        revision: API_VERSION,
      },
    });

    this.lists = [];
    this.segments = [];
  }

  /**
   * Get the authorization URL for the OAuth flow
   * @param {string} redirectUri - The redirect URI registered with Klaviyo
   * @param {string} state - Random string for CSRF protection
   * @returns {string} - The authorization URL
   */
  static getAuthorizationUrl(redirectUri, state) {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: CLIENT_ID,
      redirect_uri: redirectUri,
      scope: DEFAULT_SCOPES.join(' '),
      state,
    });

    return `${AUTH_ENDPOINT}?${params.toString()}`;
  }

  _getBasicAuthorizationHeader() {
    return getBasicAuthorizationHeader();
  }

  async getNewToken() {
    return handleTokenRefresh(
      async () => {
        const response = await axios.default.post(
          TOKEN_ENDPOINT,
          querystring.stringify({
            grant_type: 'refresh_token',
            refresh_token: this.refreshToken,
          }),
          {
            headers: {
              Authorization: this._getBasicAuthorizationHeader(),
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        const dataToWrite = response.data;
        dataToWrite.expires_at = new Date();
        dataToWrite.expires_at.setSeconds(
          dataToWrite.expires_at.getSeconds() + dataToWrite.expires_in,
        );
        delete dataToWrite.expires_in;
        await refreshTokenForAuth({ access_token: this.accessToken }, { ...dataToWrite });
        this.accessToken = dataToWrite.access_token;
        this.refreshToken = dataToWrite.refresh_token;
        this.expiresAt = dataToWrite.expires_at;
        this.api.defaults.headers.Authorization = `Bearer ${this.accessToken}`;

        return true;
      },
      {
        context: 'klaviyo-oauth-token-refresh',
        logger: log,
        returnFalseOnError: true,
      },
    );
  }

  async isTokenGood() {
    let success;
    const currentDatePlus10Mins = new Date();
    currentDatePlus10Mins.setMinutes(currentDatePlus10Mins.getMinutes() + 10);

    if (currentDatePlus10Mins >= this.expiresAt) {
      success = await this.getNewToken();
    } else {
      success = true;
    }
    return success;
  }

  async ping() {
    return executeApiRequest(
      async () => {
        const isSuccess = await this.isTokenGood();
        if (!isSuccess) {
          throw new KlaviyoError('Could not refresh token', {
            context: 'klaviyo-oauth-ping',
            code: 'KLAVIYO_TOKEN_REFRESH_FAILED',
          });
        }

        const response = await this.api.get('/lists');
        this.lists = safeParseResponse(response, this._formatList.bind(this), {
          context: 'klaviyo-oauth-ping-parse',
          logger: log,
        });

        return true;
      },
      {
        context: 'klaviyo-oauth-ping',
        logger: log,
        returnFalseOnError: true,
      },
    );
  }

  async getData() {
    try {
      const isSuccess = await this.isTokenGood();
      if (!isSuccess) {
        throw new KlaviyoError('Could not refresh token', {
          context: 'klaviyo-oauth-get-data',
          code: 'KLAVIYO_TOKEN_REFRESH_FAILED',
        });
      }

      const lists = await this.getLists();

      return { success: true, lists };
    } catch (error) {
      log.error(
        error instanceof KlaviyoError ? error.toLogFormat() : error,
        'Error getting data from Klaviyo',
      );
      return {
        success: false,
        error: error instanceof KlaviyoError ? error.message : error.message || 'Unknown error',
      };
    }
  }

  async getSegmentsAndList() {
    return executeApiRequest(
      async () => {
        const isSuccess = await this.isTokenGood();
        if (!isSuccess) {
          throw new KlaviyoError('Could not refresh token', {
            context: 'klaviyo-oauth-get-segments-and-lists',
            code: 'KLAVIYO_TOKEN_REFRESH_FAILED',
          });
        }

        await Promise.all([this.getSegments(), this.getLists()]);

        return [...this.lists, ...this.segments];
      },
      {
        context: 'klaviyo-oauth-get-segments-and-lists',
        logger: log,
      },
    );
  }

  async getSegments() {
    return executeApiRequest(
      async () => {
        const isSuccess = await this.isTokenGood();
        if (!isSuccess) {
          throw new KlaviyoError('Could not refresh token', {
            context: 'klaviyo-oauth-get-segments',
            code: 'KLAVIYO_TOKEN_REFRESH_FAILED',
          });
        }

        this.segments = [];
        let nextPage;
        do {
          const response = await this.api.get(nextPage ?? '/segments');
          const formattedData = safeParseResponse(response, this._formatList.bind(this), {
            context: 'klaviyo-oauth-get-segments-parse',
            logger: log,
          });

          this.segments.push(...formattedData);
          nextPage = response.data.links.next;
        } while (nextPage);

        return this.segments;
      },
      {
        context: 'klaviyo-oauth-get-segments',
        logger: log,
      },
    );
  }

  async getLists() {
    return executeApiRequest(
      async () => {
        const isSuccess = await this.isTokenGood();
        if (!isSuccess) {
          throw new KlaviyoError('Could not refresh token', {
            context: 'klaviyo-oauth-get-lists',
            code: 'KLAVIYO_TOKEN_REFRESH_FAILED',
          });
        }

        this.lists = [];
        let nextPage;
        do {
          const response = await this.api.get(nextPage ?? '/lists?sort=name');
          const formattedData = safeParseResponse(response, this._formatList.bind(this), {
            context: 'klaviyo-oauth-get-lists-parse',
            logger: log,
          });

          this.lists.push(...formattedData);
          nextPage = response.data.links.next;
        } while (nextPage);

        return this.lists;
      },
      {
        context: 'klaviyo-oauth-get-lists',
        logger: log,
      },
    );
  }

  _formatList({ data }) {
    return data.map((item) => {
      const { id, attributes, type: listType } = item;
      return {
        id,
        name: attributes.name,
        listType,
      };
    });
  }
}

module.exports = KlaviyoOAuthAdapter;
