const querystring = require('querystring');
const axios = require('axios');
const log = require('../../logger').child({ integration: 'keap' });
const { refreshTokenForAuth } = require('../../util/oauth');
// infusionSoft
const client_id = '2trfey2zbetac6te9xz6d62f';
const client_secret = 'HdMb4TJuuz';

class KeapAdapter {
  constructor({ access_token, refresh_token, expires_at }) {
    this.accessToken = access_token;
    this.refreshToken = refresh_token;
    this.expiresAt = expires_at;
    this.api = axios.create({
      baseURL: 'https://api.infusionsoft.com/crm/rest/v2',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  async ping() {
    try {
      const isSuccess = await this.isTokenGood();
      if (!isSuccess) {
        throw Error('Could not refresh token');
      }

      const response = await this.api.get('/businessProfile');
      return response.status === 200;
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while pinging keapOAuth:', errorMessage);
      return false;
    }
  }

  async getTags() {
    try {
      // https://api.infusionsoft.com/crm/rest/v2/tags?limit=10&offset=0
      let tags = [];
      let response = await this.api.get('/tags');
      tags = tags.concat(response.data.tags);

      if (response.data.next) {
        while (response.data.next && tags.length < response.data.count) {
          try {
            response = await this.api.request({ method: 'get', url: response.data.next });
            tags = tags.concat(response.data.tags);
          } catch (e) {
            log.error('error while getting tags', e.message);
            break;
          }
        }
      }

      return tags.map((t) => {
        return {
          id: t.id,
          name: `${t.name} (${t.id})`,
        };
      });
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while query tags keapOAuth:', errorMessage);
      return [];
    }
  }

  async getFields() {
    try {
      const contact = await this.api.get(`/contacts/model`);
      const fields = contact.data.custom_fields;
      fields.push({ id: 'Email', label: 'Email' });
      fields.push({ id: 'FirstName', label: 'First name' });
      fields.push({ id: 'LastName', label: 'Last name' });

      return fields
        .map((field) => {
          return {
            id: field.id.toString(),
            name: field.label,
          };
        })
        .sort((a, b) => (a.name > b.name ? 1 : -1));
    } catch (e) {
      log.error(e);
      return [];
    }
  }

  async getData() {
    const ret = { tags: [], fields: [] };

    try {
      const isSuccess = await this.isTokenGood();
      if (!isSuccess) {
        throw Error('Could not refresh token');
      }
    } catch (e) {
      log.error(e);
      return ret;
    }

    ret.tags = await this.getTags();
    ret.fields = await this.getFields();

    return ret;
  }

  async getNewToken() {
    let response;
    try {
      response = await axios.default.post(
        'https://api.infusionsoft.com/token',
        querystring.stringify({
          grant_type: 'refresh_token',
          refresh_token: this.refreshToken,
          client_id,
          client_secret,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while getting new token from keapOAuth:', errorMessage);
      return false;
    }

    const dataToWrite = response.data;
    dataToWrite.expires_at = new Date();
    dataToWrite.expires_at.setSeconds(dataToWrite.expires_at.getSeconds() + dataToWrite.expires_in);
    delete dataToWrite.expires_in;
    await refreshTokenForAuth({ access_token: this.accessToken }, { ...dataToWrite });
    this.accessToken = dataToWrite.access_token;
    this.refreshToken = dataToWrite.refresh_token;
    this.expiresAt = dataToWrite.expires_at;
    this.api.defaults.headers.Authorization = `Bearer ${this.accessToken}`;

    return true;
  }

  async isTokenGood() {
    let success;
    const currentDatePlus10Mins = new Date();
    currentDatePlus10Mins.setMinutes(currentDatePlus10Mins.getMinutes() + 10);

    if (currentDatePlus10Mins >= this.expiresAt) {
      success = await this.getNewToken();
    } else {
      success = true;
    }
    return success;
  }
}

// ;(async () => {
//   let expires_at = new Date()
//   expires_at.setHours(15, 40, 0)
//   let infusionSoftAdapter = new InfusionSoftAdapter({access_token: 'afj4n6d8fdn24nqh4twe96rg', refresh_token: '22beer4ag44sszhwxpumaytq', expires_at})
//   let canConnect = await infusionSoftAdapter.ping()
//   console.log('canConnect', canConnect)
//   if (canConnect) {
//     let tags = await infusionSoftAdapter.getData()
//     console.log('tags', tags)
//   }
// })()

module.exports = KeapAdapter;
