const { RECOMMENDATION_STATES } = require('@om/integrations');
const RecommendationStateResolver = require('./RecommendationStateResolver');

describe('RecommendationStateResolver test', () => {
  test('No active campaign with same integration', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('No active campaign with klaviyoOAuth integration', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyoOAuth';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('No active campaign with klaviyoOAuth integration, klaviyo detected on domain', () => {
    const integrationsCount = 0;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyoOAuth';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
      true,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.ADD);
  });

  test('Has global integration with klaviyoOAuth integration, but klaviyoOAuth not detected on domain', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 0;
    const integrationType = 'klaviyoOAuth';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.NONE);
  });

  test('Has 1 global integration with non-klaviyo integration', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 1;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.FINISH);
  });

  test('Has 1 global integration with non-klaviyo integration in multiple campaigns', () => {
    const integrationsCount = 1;
    const campaignsWithIntegration = 4;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.FINISH);
  });

  test('Has multiple global integration with non-klaviyo integration', () => {
    const integrationsCount = 3;
    const campaignsWithIntegration = 4;
    const integrationType = 'nonKlaviyo';
    const resolver = new RecommendationStateResolver(
      integrationType,
      integrationsCount,
      campaignsWithIntegration,
    );

    expect(resolver.getRecommendationState()).toEqual(RECOMMENDATION_STATES.SELECT);
  });
});
