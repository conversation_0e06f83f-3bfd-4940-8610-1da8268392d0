import { AbstractIntegrationSettings } from '@/services/integrations/settings/AbstractIntegrationSettings';

class KlaviyoOAuthSettings extends AbstractIntegrationSettings {
  constructor() {
    super();
    this.type = 'klaviyoOAuth';
  }

  setFromExisting(integrationData) {
    this.id = integrationData?.id || ''; // global id
    this.publicApiKey = integrationData?.global?.data?.publicApiKey || '';
    this.access_token = integrationData?.global?.data?.access_token || '';
    this.refresh_token = integrationData?.global?.data?.refresh_token || '';
    this.expires_at = integrationData?.global?.data?.expires_at || '';
    this.name = integrationData?.global?.data?.name || '';
    this.campaignIntegrationId = integrationData._id || '';

    this.settings = integrationData?.settings || [];
    this.bindings = integrationData?.bindings || [];

    this.convertedSettings = this.settings.reduce((previousObject, setting) => {
      return {
        [setting.key]: setting.value,
        ...previousObject,
      };
    }, integrationData?.convertedSettings || {});
  }

  getGlobals() {
    return {
      publicApiKey: this.publicApiKey,
      name: this.name,
      access_token: this.access_token,
      refresh_token: this.refresh_token,
      expires_at: this.expires_at,
    };
  }

  getCampaignSettings() {
    const settings = [];

    if (this.convertedSettings.listId) {
      settings.push({
        key: 'listId',
        value: this.convertedSettings.listId,
      });
    }

    if (this.convertedSettings.multiList) {
      settings.push({
        key: 'multiList',
        value: this.convertedSettings.multiList,
      });
    }

    return {
      settings,
      bindings: this.bindings,
    };
  }
}

export default KlaviyoOAuthSettings;
