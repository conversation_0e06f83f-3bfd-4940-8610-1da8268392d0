<template lang="pug">
.content-step2
  loading-logo(v-if="loading")
  template(v-else)
    .content-wrapper-klaviyo(:class="{ 'multi-list-disable': !isMultiListEnabled }")
      om-body-text.mr-2(bt400sm) {{ $t('integrationFlow.integrationModal.general.listHelper', { type: 'Klaviyo' }) }}
      .d-flex(v-if="isMultiListEnabled || isExisting")
        om-body-text.mr-2(bt400sm) {{ $t('integrationFlow.klaviyo.multiListCampaign') }}
        om-switch#multiListSwitch(v-model="multiListToggle")
        om-tooltip(color="#8F97A4" iconSize="1.5rem") {{ $t('integrationFlow.klaviyo.multiListCampaignTooltip') }}
      transition-group(name="fade")
        template(v-if="multiListToggle")
          .multi-list-group(key="multi-list")
            .multi-list-group-item(v-for="(multiListItem, index) in multiListNames")
              .multi-list-group-item-title {{ $t(`integrationFlow.klaviyo.${multiListItem}`) }}
              om-select(
                :id="`multiList-${multiListItem}`"
                v-model="multiList[multiListItem]"
                :options="lists"
                :placeholder="$t('integrationFlow.integrationModal.selectAList')"
                @input="onMultiListChange(multiListItem)"
              )
        template(v-else)
          om-select#listName(
            key="single-list"
            v-model="listId"
            :options="lists"
            :placeholder="$t('integrationFlow.integrationModal.selectAList')"
            :error="isListInvalidByAlert"
            @input="onListChange"
          )
          #listName.error-msg.mt-3(v-if="isListInvalidByAlert" key="error-message")
            span.text-danger(v-html="$t('integrationFlow.integrationModal.general.invalidList')")
</template>
<script>
  import validationByAlert from '@/mixins/integration/validationByAlert';
  import trackStep2Mixin from '@/mixins/integration/trackStep2';
  import campaignFieldsMixin from '@/mixins/integration/campaignFields';
  import { isFeatureEnabled, MULTI_LIST_CAMPAIGN } from '@/utils/features';
  import { mapState } from 'vuex';
  import { IntegrationService } from '@/services/integrations/integrationService';

  const MULTI_LIST_TYPES = {
    EMAIL_ONLY: 'emailOnly',
    PHONE_ONLY: 'phoneOnly',
    EMAIL_AND_PHONE: 'emailAndPhone',
  };

  export default {
    mixins: [campaignFieldsMixin, validationByAlert, trackStep2Mixin],
    props: {
      settings: {
        type: Object,
        required: true,
      },

      validations: {
        type: Object,
        required: true,
      },
    },

    data() {
      return {
        step2Fields: [
          'listId',
          `multiList.${MULTI_LIST_TYPES.EMAIL_ONLY}`,
          `multiList.${MULTI_LIST_TYPES.PHONE_ONLY}`,
          `multiList.${MULTI_LIST_TYPES.EMAIL_AND_PHONE}`,
        ],
        loading: false,
        listId: null,
        lists: [],
        hasError: false,
        multiListToggle: false,
        multiListNames: [
          MULTI_LIST_TYPES.EMAIL_ONLY,
          MULTI_LIST_TYPES.PHONE_ONLY,
          MULTI_LIST_TYPES.EMAIL_AND_PHONE,
        ],
        multiList: {
          [MULTI_LIST_TYPES.EMAIL_ONLY]: null,
          [MULTI_LIST_TYPES.PHONE_ONLY]: null,
          [MULTI_LIST_TYPES.EMAIL_AND_PHONE]: null,
        },
        campaignFields: [],
        isExisting: false,
      };
    },

    computed: {
      ...mapState(['account']),
      isListInvalidByAlert() {
        return !this.validByAlert.list;
      },

      features() {
        return this.account?.features ?? [];
      },

      isMultiListFeatureFlagEnabled() {
        return isFeatureEnabled(this.features, MULTI_LIST_CAMPAIGN);
      },

      isMultiListEnabled() {
        return this.isMultiListFeatureFlagEnabled && this.hasEmailAndPhoneField;
      },
    },

    watch: {
      listId(option) {
        if (!option) return;

        this.updateListSetting(option.key);
      },

      multiListToggle(toggleValue) {
        const settings = this.settings;

        if (toggleValue) {
          delete settings.convertedSettings.listId;
          this.setListId(null);
          this.$emit('update:settings', { ...settings });
          return;
        }

        const index = settings.settings.findIndex((item) => item.key === 'multiList');
        settings.settings.splice(index, 1);
        delete settings.convertedSettings.multiList;
        this.setMultiListId(MULTI_LIST_TYPES.EMAIL_ONLY, null);
        this.setMultiListId(MULTI_LIST_TYPES.PHONE_ONLY, null);
        this.setMultiListId(MULTI_LIST_TYPES.EMAIL_AND_PHONE, null);
        this.$emit('update:settings', { ...settings });
      },
    },

    async mounted() {
      this.loading = true;
      if (this.isMultiListFeatureFlagEnabled) {
        this.campaignFields = await this.fetchCampaignFields();
      }
      // IMPORTANT to load data then set model (behavior of om-select)
      const lists = await this.loadLists();

      if (!lists.length) {
        this.updateListSetting(null);
        this.loading = false;
        return;
      }

      this.lists = lists;

      if (this.settings.convertedSettings.hasOwnProperty('multiList')) {
        const multiLists = this.settings.convertedSettings.multiList;
        this.multiListToggle = true;
        this.setMultiListId(MULTI_LIST_TYPES.EMAIL_ONLY, multiLists.emailOnly);
        this.setMultiListId(MULTI_LIST_TYPES.PHONE_ONLY, multiLists.phoneOnly);
        this.setMultiListId(MULTI_LIST_TYPES.EMAIL_AND_PHONE, multiLists.emailAndPhone);
        this.isExisting = true;
      } else {
        const key = this.settings.convertedSettings.listId;
        // om-select selected option only needs "key" from object
        this.setListId(key);
      }
      this.loading = false;
      this.storeInitialStep2State();
    },

    methods: {
      async loadLists() {
        const integrationService = new IntegrationService(this.$apollo);
        let lists = [];

        try {
          const result = await integrationService.fetchIntegrationData(
            this.settings.type,
            this.settings.id,
          );

          if (result.success) {
            lists = result.lists.map((list) => ({
              key: list.id,
              value: list.name,
            }));
          } else {
            this.errorNotification(result.error);
          }
        } catch (e) {
          this.errorNotification(e.message);
        }

        return lists;
      },

      errorNotification(message) {
        this.hasError = true;
        this.$notify({
          type: 'error',
          text: message,
        });
        setTimeout(() => {
          this.$bus.$emit('integration-show-first-step');
        }, 2000);
      },

      handleMultiListChange(type) {
        this.updateMultiListSetting(type, this.multiList[type]);
      },

      updateMultiListSetting(type, value) {
        const settings = this.settings;
        if (!settings.convertedSettings.multiList) {
          settings.convertedSettings.multiList = {};
        }

        settings.convertedSettings.multiList[type] = value.key;
        this.$emit('update:settings', { ...settings });
        this.emitIsModified();
      },

      updateListSetting(value) {
        const settings = this.settings;
        settings.convertedSettings.listId = value;
        delete settings.convertedSettings?.multiList;
        this.$emit('update:settings', { ...settings });
        this.emitIsModified();
      },

      setListId(value) {
        this.listId = value ? { key: value } : null;
      },

      setMultiListId(multiListType, value) {
        this.multiList[multiListType] = value ? { key: value } : null;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .content-wrapper-klaviyo {
    .multi-list-group {
      margin-top: 1rem;
      .multi-list-group-item {
        margin-bottom: 1rem;
        .multi-list-group-item-title {
          margin-bottom: 0.5rem;
          font-weight: 500;
        }
      }
    }
  }
</style>
